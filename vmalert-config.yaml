apiVersion: operator.victoriametrics.com/v1beta1
kind: VMAlert
metadata:
  name: vmalert-monitoring
  namespace: monitoring
spec:
  # 副本数
  replicaCount: 2
  
  # 资源配置
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "300m"
  
  # 数据源配置
  datasource:
    url: "http://vmselect:8481/select/0/prometheus"
    
  # 通知配置
  notifier:
    url: "http://alertmanager:9093"
    
  # 远程写入配置（可选，用于记录告警状态）
  remoteWrite:
    url: "http://vminsert:8480/insert/0/prometheus"
    
  # 告警规则配置
  ruleSelector:
    matchLabels:
      app: alert-rules
      
  # 评估间隔
  evaluationInterval: "30s"
  
  # 外部标签
  externalLabels:
    cluster: "production"
    region: "us-west-1"
    
  # Web配置
  web:
    externalURL: "http://vmalert.example.com"
    
---
apiVersion: operator.victoriametrics.com/v1beta1
kind: VMRule
metadata:
  name: monitoring-rules
  namespace: monitoring
  labels:
    app: alert-rules
spec:
  groups:
    # Kubernetes集群告警规则
    - name: kubernetes-cluster
      interval: 30s
      rules:
        # 节点状态检查
        - alert: KubernetesNodeNotReady
          expr: kube_node_status_condition{condition="Ready",status="true"} == 0
          for: 5m
          labels:
            severity: critical
            level: "1"
          annotations:
            summary: "Kubernetes节点未就绪"
            description: "节点 {{ $labels.node }} 状态为NotReady超过5分钟"
            
        # Pod状态检查
        - alert: KubernetesPodCrashLooping
          expr: rate(kube_pod_container_status_restarts_total[15m]) * 60 * 15 > 0
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "Pod崩溃循环"
            description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} 容器 {{ $labels.container }} 正在崩溃循环"
            
        # PVC存储空间
        - alert: KubernetesPersistentVolumeClaimPending
          expr: kube_persistentvolumeclaim_status_phase{phase="Pending"} == 1
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "PVC处于Pending状态"
            description: "PVC {{ $labels.namespace }}/{{ $labels.persistentvolumeclaim }} 处于Pending状态超过5分钟"

    # 应用程序告警规则
    - name: application-alerts
      interval: 30s
      rules:
        # HTTP错误率
        - alert: HighHTTPErrorRate
          expr: |
            (
              sum(rate(nginx_http_requests_total{status=~"5.."}[5m])) by (instance)
              /
              sum(rate(nginx_http_requests_total[5m])) by (instance)
            ) * 100 > 5
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "HTTP错误率过高"
            description: "实例 {{ $labels.instance }} HTTP 5xx错误率超过5%，当前值: {{ $value }}%"
            
        # 响应时间过长
        - alert: HighResponseTime
          expr: |
            histogram_quantile(0.95,
              sum(rate(nginx_http_request_duration_seconds_bucket[5m])) by (le, instance)
            ) > 1
          for: 10m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "响应时间过长"
            description: "实例 {{ $labels.instance }} 95%分位响应时间超过1秒，当前值: {{ $value }}秒"
            
        # 连接数过多
        - alert: TooManyConnections
          expr: nginx_connections_active > 1000
          for: 10m
          labels:
            severity: info
            level: "3"
          annotations:
            summary: "活跃连接数过多"
            description: "实例 {{ $labels.instance }} 活跃连接数超过1000，当前值: {{ $value }}"

    # 基础设施告警规则
    - name: infrastructure-alerts
      interval: 30s
      rules:
        # 文件描述符使用率
        - alert: HighFileDescriptorUsage
          expr: |
            (
              process_open_fds / process_max_fds
            ) * 100 > 80
          for: 10m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "文件描述符使用率过高"
            description: "实例 {{ $labels.instance }} 文件描述符使用率超过80%，当前值: {{ $value }}%"
            
        # 网络接收错误
        - alert: NetworkReceiveErrors
          expr: rate(node_network_receive_errs_total[5m]) > 10
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "网络接收错误"
            description: "节点 {{ $labels.instance }} 网卡 {{ $labels.device }} 接收错误率过高，当前值: {{ $value }}/秒"
            
        # 网络传输错误
        - alert: NetworkTransmitErrors
          expr: rate(node_network_transmit_errs_total[5m]) > 10
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "网络传输错误"
            description: "节点 {{ $labels.instance }} 网卡 {{ $labels.device }} 传输错误率过高，当前值: {{ $value }}/秒"

    # 存储告警规则
    - name: storage-alerts
      interval: 30s
      rules:
        # 磁盘IO等待时间
        - alert: HighDiskIOWait
          expr: rate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
          for: 10m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "磁盘IO等待时间过长"
            description: "节点 {{ $labels.instance }} IO等待时间超过20%，当前值: {{ $value }}%"
            
        # 磁盘读取错误
        - alert: DiskReadErrors
          expr: rate(node_disk_read_errors_total[5m]) > 0
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "磁盘读取错误"
            description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.device }} 出现读取错误"
            
        # 磁盘写入错误
        - alert: DiskWriteErrors
          expr: rate(node_disk_write_errors_total[5m]) > 0
          for: 5m
          labels:
            severity: warning
            level: "2"
          annotations:
            summary: "磁盘写入错误"
            description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.device }} 出现写入错误"

---
apiVersion: v1
kind: Service
metadata:
  name: vmalert
  namespace: monitoring
spec:
  selector:
    app.kubernetes.io/name: vmalert
    app.kubernetes.io/instance: vmalert-monitoring
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  type: ClusterIP
