# 定义 realip 变量
map $http_x_forwarded_for $realip {
    default   $remote_addr;
    ~^(?P<firstAddr>[0-9\.]+),?.*$  $firstAddr;
}

map $http_stgw_request_id $trace_id {
    default   $http_stgw_request_id;
    ""        $request_id;
}

# 优化的日志格式 - 平衡详细度和性能
log_format optimized_log escape=json '{'
    '"time":"$time_iso8601",'
    '"client":"$realip",'
    '"port":"$remote_port",'
    '"method":"$request_method",'
    '"uri":"$request_uri",'
    '"status":"$status",'
    '"size":"$body_bytes_sent",'
    '"reqtime":"$request_time",'
    '"ua":"$http_user_agent",'
    '"ref":"$http_referer",'
    '"xff":"$http_x_forwarded_for",'
    '"trace":"$trace_id",'
    '"ups_addr":"$upstream_addr",'
    '"ups_status":"$upstream_status",'
    '"ups_time":"$upstream_response_time",'
    '"ups_cache":"$upstream_cache_status",'
    '"proto":"$server_protocol",'
    '"host":"$host",'
    '"scheme":"$scheme"'
'}';

# 保留原有日志格式作为备份
log_format detailed escape=json '{'
    '"timestamp":"$time_iso8601",'
    '"remote_addr":"$realip",'
    '"remote_user":"$remote_user",'
    '"request":"$request",'
    '"request_method":"$request_method",'
    '"request_uri":"$request_uri",'
    '"uri":"$uri",'
    '"query_string":"$query_string",'
    '"request_length":"$request_length",'
    '"status":"$status",'
    '"bytes_sent":"$bytes_sent",'
    '"body_bytes_sent":"$body_bytes_sent",'
    '"http_referer":"$http_referer",'
    '"http_user_agent":"$http_user_agent",'
    '"http_x_forwarded_for":"$http_x_forwarded_for",'
    '"http_x_real_ip":"$http_x_real_ip",'
    '"stgw_request_id":"$trace_id",'
    '"upstream_addr":"$upstream_addr",'
    '"upstream_response_time":"$upstream_response_time",'
    '"upstream_connect_time":"$upstream_connect_time",'
    '"upstream_header_time":"$upstream_header_time",'
    '"upstream_status":"$upstream_status",'
    '"request_time":"$request_time",'
    '"ssl_protocol":"$ssl_protocol",'
    '"ssl_cipher":"$ssl_cipher",'
    '"scheme":"$scheme",'
    '"server_protocol":"$server_protocol",'
    '"pipe":"$pipe",'
    '"gzip_ratio":"$gzip_ratio",'
    '"http_host":"$http_host"'
'}';

# 完整详细日志格式 - 仅在需要深度分析时使用
log_format full_detail escape=json '{'
    '"time":"$time_iso8601",'
    '"client":"$realip",'
    '"port":"$remote_port",'
    '"user":"$remote_user",'
    '"req":"$request",'
    '"method":"$request_method",'
    '"uri":"$request_uri",'
    '"path":"$uri",'
    '"query":"$query_string",'
    '"req_len":"$request_length",'
    '"status":"$status",'
    '"sent":"$bytes_sent",'
    '"body_sent":"$body_bytes_sent",'
    '"type":"$content_type",'
    '"ref":"$http_referer",'
    '"ua":"$http_user_agent",'
    '"xff":"$http_x_forwarded_for",'
    '"real_ip":"$http_x_real_ip",'
    '"trace":"$trace_id",'
    '"ups_addr":"$upstream_addr",'
    '"ups_time":"$upstream_response_time",'
    '"ups_conn":"$upstream_connect_time",'
    '"ups_head":"$upstream_header_time",'
    '"ups_status":"$upstream_status",'
    '"ups_cache":"$upstream_cache_status",'
    '"req_time":"$request_time",'
    '"conn":"$connection",'
    '"conn_req":"$connection_requests",'
    '"ssl":"$ssl_protocol",'
    '"cipher":"$ssl_cipher",'
    '"scheme":"$scheme",'
    '"proto":"$server_protocol",'
    '"srv_addr":"$server_addr",'
    '"srv_port":"$server_port",'
    '"pipe":"$pipe",'
    '"gzip":"$gzip_ratio",'
    '"vhost":"$http_host",'
    '"host":"$host"'
'}';

# 访问日志配置 - 使用优化的日志格式
access_log /usr/local/openresty/nginx/logs/access.log optimized_log
    buffer=64k
    flush=5s
    gzip=1;

# 错误日志级别
error_log /usr/local/openresty/nginx/logs/error.log error;
