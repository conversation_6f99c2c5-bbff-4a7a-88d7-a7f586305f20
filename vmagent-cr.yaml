apiVersion: operator.victoriametrics.com/v1beta1
kind: VMAgent
metadata:
  name: vmagent-monitoring
  namespace: monitoring
spec:
  # 副本数
  replicaCount: 2

  # 资源配置
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"

  # 远程写入配置
  remoteWrite:
    - url: "http://vminsert:8480/insert/0/prometheus/"

  # 抓取配置
  scrapeConfigs:
    # Kubernetes API Server 监控
    - job_name: 'kubernetes-apiservers'
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      kubernetes_sd_configs:
        - role: endpoints
      relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https

    # Kubernetes Nodes 监控
    - job_name: 'kubernetes-nodes'
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      kubernetes_sd_configs:
        - role: node
      relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics

    # CoreDNS 监控
    - job_name: 'OPSClusterCoreDNS'
      kubernetes_sd_configs:
        - role: pod
      relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_k8s_app]
          action: keep
          regex: kube-system;kube-dns
        - action: replace
          target_label: __metrics_path__
          replacement: /metrics
        - action: replace
          target_label: __address__
          replacement: ${1}:9153
          source_labels: [__meta_kubernetes_pod_ip]
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: instance
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace

    # ETCD 监控
    - job_name: 'OPSClusterEtcd'
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      kubernetes_sd_configs:
        - role: pod
      relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_component]
          action: keep
          regex: kube-system;etcd
        - source_labels: [__meta_kubernetes_pod_ip]
          action: replace
          target_label: __address__
          replacement: ${1}:2379
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: instance
        - action: replace
          target_label: __metrics_path__
          replacement: /metrics
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)

    # Nginx 监控
    - job_name: 'nginx-monitoring'
      static_configs:
        - targets: ['nginx:80']
      metrics_path: /metrics
      scrape_interval: 30s

    # Node Exporter 监控
    - job_name: 'node-exporter'
      kubernetes_sd_configs:
        - role: pod
      relabel_configs:
        - source_labels: [__meta_kubernetes_pod_label_app]
          action: keep
          regex: node-exporter
        - source_labels: [__meta_kubernetes_pod_ip]
          action: replace
          target_label: __address__
          replacement: ${1}:9100