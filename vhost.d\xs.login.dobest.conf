map $http_stgw_request_id $trace_id {
    default   $http_stgw_request_id;
    ""        $request_id;
}

server {
	    listen                              80;
	    server_name                         xs-login.dobest.com;
	    keepalive_timeout                   60;

	access_log                          /opt/logs/nginx/xs-login/xs-login.dobest.com-upstream.log UPSTREAM;

	include                             xff.conf;
	include                             global.conf;

	gzip_disable                        "MSIE [1-6]\.";

	proxy_set_header                    Host $http_host;
	proxy_connect_timeout               200ms;
	proxy_http_version                  1.1;
	proxy_set_header                    Connection "";

	fastcgi_read_timeout                5;
	fastcgi_connect_timeout             300ms;
	fastcgi_keep_conn                   on;

	add_header                          P3P "CP=CAO PSA OUR";
    proxy_set_header stgw_request_id $trace_id;
    add_header stgw_request_id $trace_id always;
    
	location /pre-aitd/security {
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
	    #proxy_set_header                    Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_pass                      http://pre-aitd.dobest.cn/security/;
	}

	location /health {
	       return 403;
	}
}
