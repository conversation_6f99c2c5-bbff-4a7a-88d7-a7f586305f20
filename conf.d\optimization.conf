# 基础优化配置
sendfile on;
tcp_nopush on;
tcp_nodelay on;
server_tokens off;
keepalive_timeout 65;
keepalive_requests 1000;
reset_timedout_connection on;
client_body_timeout 12;
client_header_timeout 12;
send_timeout 10;

# 缓冲区配置 - 参考32G内存
client_body_buffer_size 32k;
client_max_body_size 20m;
client_header_buffer_size 2k;
large_client_header_buffers 4 8k;
output_buffers 2 32k;
postpone_output 1460;

# 文件缓存配置
open_file_cache max=10000 inactive=30s;
open_file_cache_valid 60s;
open_file_cache_min_uses 2;
open_file_cache_errors on;

# FastCGI 优化
fastcgi_buffers 32 32k;
fastcgi_buffer_size 64k;
fastcgi_connect_timeout 300;
fastcgi_send_timeout 300;
fastcgi_read_timeout 300;
fastcgi_busy_buffers_size 128k;
fastcgi_temp_file_write_size 256k;
fastcgi_intercept_errors on;

# 散列表优化
variables_hash_max_size 2048;
variables_hash_bucket_size 128;
types_hash_max_size 2048;
types_hash_bucket_size 64;
server_names_hash_bucket_size 128;

# Gzip 配置
gzip_static on;
gzip on;
gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_min_length 1000;
gzip_buffers 16 8k;
gzip_http_version 1.1;
gzip_types
    application/javascript
    application/json
    application/xml
    application/x-javascript
    text/css
    text/javascript
    text/plain
    text/xml
    text/x-component
    application/x-httpd-php;
