- job_name: 'OPSClusterEtcd'
  scheme: https
  tls_config:
    # 使用 Kubernetes 服务账户的 CA 证书
    ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    # 如果 etcd 使用自签名证书，可能需要跳过验证
    insecure_skip_verify: true
  bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
  kubernetes_sd_configs:
    - role: pod
  relabel_configs:
    # 选择 etcd pods
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_component]
      action: keep
      regex: kube-system;etcd
    # 设置目标地址为 pod IP:端口
    - source_labels: [__meta_kubernetes_pod_ip]
      action: replace
      target_label: __address__
      replacement: ${1}:2379
    # 添加实例标签
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: instance
    # 设置指标路径
    - action: replace
      target_label: __metrics_path__
      replacement: /metrics
    # 保留有用的标签
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)