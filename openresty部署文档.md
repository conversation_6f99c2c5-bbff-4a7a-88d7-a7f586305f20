# OpenResty 部署文档

本文档详细介绍了 OpenResty 的安装、配置和优化过程，适用于生产环境部署。

## 1. 安装依赖项

首先确保系统已经安装了必要的依赖项，如开发工具和库。

```bash
sudo yum groupinstall "Development Tools" -y
sudo yum install pcre pcre-devel zlib zlib-devel openssl openssl-devel gcc gcc-c++ make -y
sudo yum install wget curl git -y
```

## 2. 安装 OpenResty

### 2.1 下载 OpenResty 安装包并解压

```bash
cd /usr/local/src
sudo wget https://openresty.org/download/openresty-********.tar.gz
sudo tar -zxvf openresty-********.tar.gz
cd openresty-********
```

### 2.2 配置编译选项(新)

自动处理依赖问题 无需手动指定

```bash
sudo ./configure \
  --prefix=/usr/local/openresty \
  --with-cc-opt='-O2 -I/usr/local/openresty/zlib/include -I/usr/local/openresty/pcre/include -I/usr/local/openresty/openssl111/include' \
  --with-ld-opt='-Wl,-rpath,/usr/local/lib' \
  --with-cc='ccache gcc -fdiagnostics-color=always' \
  --with-pcre-jit \
  --with-http_ssl_module \
  --with-http_realip_module \
  --with-http_addition_module \
  --with-http_sub_module \
  --with-http_dav_module \
  --with-http_flv_module \
  --with-http_mp4_module \
  --with-http_gunzip_module \
  --with-http_gzip_static_module \
  --with-http_auth_request_module \
  --with-http_random_index_module \
  --with-http_secure_link_module \
  --with-http_stub_status_module \
  --with-http_v2_module \
  --with-http_slice_module \
  --with-stream \
  --with-stream_ssl_module \
  --with-stream_ssl_preread_module \
  --with-threads
```

### 2.3 编译并安装 OpenResty

```bash
sudo make
sudo make install
```

### 2.4 处理常见编译错误

如果遇到 LuaJIT 相关错误，执行以下命令：

```bash
cd /usr/local/src
sudo wget https://github.com/openresty/luajit2/archive/refs/tags/v2.1-20210510.tar.gz
sudo tar -xvzf v2.1-20210510.tar.gz 
cd luajit2-2.1-20210510/
sudo make && sudo make install 
```

安装完成后，LuaJIT 库通常会安装到 `/usr/local/lib` 和 `/usr/local/openresty/luajit/lib`。

如果遇到 ngx_devel_kit 相关错误，执行以下命令：

```bash
cd /usr/local/src
sudo git clone https://github.com/vision5/ngx_devel_kit.git
cd ngx_devel_kit
sudo make
```

### 2.5 OpenResty 编译选项对比

在 CentOS 7.9 环境下，以下是两种不同的 OpenResty 编译配置对比，可以根据需求选择合适的编译选项：

#### 2.5.1 配置选项对比

| 特性/模块 | 之前配置 | 最新配置 | 说明 |
|---------|---------|---------|------|
| 基础编译选项 | `-O2 -DNGX_LUA_ABORT_AT_PANIC` | `-O2 -O2` | 最新配置移除了 LUA 恐慌中止选项，重复了 O2 优化 |
| 编译器选项 | `--with-cc='ccache gcc -fdiagnostics-color=always'` | 保留相同 | 两者都使用 ccache 加速编译并启用彩色诊断 |
| HTTP/3 支持 | `--with-http_v3_module` | 未包含 | 之前配置支持 HTTP/3 协议 |
| HTTP 切片模块 | `--with-http_slice_module` | 已包含 | 最新配置也包含切片模块 |
| 兼容性选项 | `--with-compat` | 未包含 | 之前配置启用了兼容性选项 |
| RDS 模块 | 未包含 | `--add-module=../rds-json-nginx-module-0.16` 和 `--add-module=../rds-csv-nginx-module-0.09` | 最新配置添加了 RDS JSON 和 CSV 模块 |
| 运行时路径 | 包含更多库路径 | 简化的库路径 | 最新配置使用了更简化的库路径设置 |



#### 2.5.2 各模块功能说明

| 模块名称 | 功能描述 |
|---------|---------|
| `ngx_devel_kit` | Nginx 开发工具包，为其他模块提供基础功能 |
| `echo-nginx-module` | 提供各种 echo/sleep/time/exec 等功能 |
| `xss-nginx-module` | 跨站脚本过滤模块 |
| `ngx_coolkit` | 提供额外的 Nginx 变量和功能 |
| `set-misc-nginx-module` | 提供各种 set_xxx 指令 |
| `form-input-nginx-module` | 解析 POST 和 PUT 请求体中的表单数据 |
| `encrypted-session-nginx-module` | 提供加密会话支持 |
| `srcache-nginx-module` | 提供透明缓存层 |
| `ngx_lua` | 在 Nginx 中嵌入 Lua 脚本 |
| `ngx_lua_upstream` | 动态操作上游服务器 |
| `headers-more-nginx-module` | 设置、添加和清除 HTTP 请求和响应头 |
| `array-var-nginx-module` | 支持数组类型变量 |
| `memc-nginx-module` | Memcached 客户端 |
| `redis2-nginx-module` | Redis 2.0 协议客户端 |
| `redis-nginx-module` | Redis 客户端 |
| `rds-json-nginx-module` | 将 RDS 输出转换为 JSON 格式 |
| `rds-csv-nginx-module` | 将 RDS 输出转换为 CSV 格式 |
| `ngx_stream_lua` | 在 Stream 模块中嵌入 Lua 脚本 |
| `http_v3_module` | HTTP/3 协议支持 |
| `http_slice_module` | 将请求拆分为子请求，支持大文件范围请求 |

#### 2.5.3 CentOS 7.9 特定注意事项

在 CentOS 7.9 环境中部署 OpenResty 时，需要注意以下几点：

1. **系统库版本**：CentOS 7.9 的系统库较旧，可能需要手动编译安装新版本的 PCRE、OpenSSL 和 zlib。

2. **GCC 版本**：默认的 GCC 版本较旧，如需使用 HTTP/3，建议升级 GCC：
   
   ```bash
   sudo yum install centos-release-scl
   sudo yum install devtoolset-9-gcc devtoolset-9-gcc-c++
   scl enable devtoolset-9 bash
```
   
3. **内核版本**：如需使用 TCP BBR 拥塞控制算法，需要升级内核至 4.9 以上版本：
   
   ```bash
   # 安装 ELRepo 仓库
   sudo rpm --import https://www.elrepo.org/RPM-GPG-KEY-elrepo.org
   sudo yum install https://www.elrepo.org/elrepo-release-7.el7.elrepo.noarch.rpm
   
   # 安装最新的主线稳定内核
   sudo yum --enablerepo=elrepo-kernel install kernel-ml
   
   # 设置 GRUB 默认启动项
   sudo grub2-set-default 0
   sudo grub2-mkconfig -o /boot/grub2/grub.cfg
   
   # 重启系统
   sudo reboot
```
   
4. **SELinux 配置**：如果启用了 SELinux，需要设置正确的上下文：
   ```bash
   sudo semanage port -a -t http_port_t -p tcp 443
   sudo semanage port -a -t http_port_t -p tcp 80
   sudo chcon -Rt httpd_sys_content_t /usr/local/openresty/nginx/html/
   sudo chcon -Rt httpd_sys_rw_content_t /usr/local/openresty/nginx/logs/
   ```

## 3. 配置 OpenResty 和 Nginx

### 3.1 创建必要的目录结构

```bash
# 创建所有必要的目录
sudo mkdir -p /usr/local/openresty/nginx/conf/conf.d
sudo mkdir -p /usr/local/openresty/nginx/conf/vhost.d
sudo mkdir -p /usr/local/openresty/nginx/conf/upstream.d
sudo mkdir -p /usr/local/openresty/nginx/logs
sudo mkdir -p /usr/local/openresty/nginx/cache/nginx
sudo mkdir -p /usr/local/openresty/nginx/cache/proxy
sudo mkdir -p /usr/local/openresty/nginx/cache/fcgi
sudo mkdir -p /var/log/nginx
```

### 3.2 创建 OpenResty 用户和组

```bash
sudo groupadd nginx
sudo useradd -g nginx nginx
```

### 3.3 修改权限

```bash
sudo chown -R nginx:nginx /usr/local/openresty
sudo chown -R nginx:nginx /usr/local/openresty/nginx/logs
sudo chown -R nginx:nginx /usr/local/openresty/nginx/cache
sudo chown -R nginx:nginx /var/log/nginx
```

### 3.4 创建基本配置文件

#### 3.4.1 主配置文件 (nginx.conf)

```bash
sudo vi /usr/local/openresty/nginx/conf/nginx.conf
```

添加以下内容：

```nginx
# 主配置文件
user nginx;

# CPU 相关优化
worker_processes auto;                    
worker_cpu_affinity auto;                 
worker_priority -10;                      

# 文件句柄和核心转储
worker_rlimit_nofile 1048576;                     
worker_shutdown_timeout 30s;              

# 性能优化
timer_resolution 50ms;                    
thread_pool default threads=64;           

# 错误日志
error_log /usr/local/openresty/nginx/logs/error.log notice;
pid /usr/local/openresty/nginx/logs/nginx.pid;

# 工作进程配置
events {
    use epoll;                            
    worker_connections 65535;             
    multi_accept on;                      
    accept_mutex off;                     
    accept_mutex_delay 50ms;              
}

# HTTP核心配置
http {
    include /usr/local/openresty/nginx/conf/mime.types;
    default_type application/octet-stream;
    charset utf-8;
    
    # 这里是 aio 指令的正确位置
    aio threads=default;
    
    # 包含优化配置
    include /usr/local/openresty/nginx/conf/conf.d/optimization.conf;
    
    # 包含SSL配置
    include /usr/local/openresty/nginx/conf/conf.d/ssl.conf;
    
    # 包含基础配置
    include /usr/local/openresty/nginx/conf/conf.d/basic.conf;
    
    # 包含日志配置
    include /usr/local/openresty/nginx/conf/conf.d/logging.conf;
    
    # 包含限流配置
    include /usr/local/openresty/nginx/conf/conf.d/limits.conf;
    
    # 包含代理配置
    include /usr/local/openresty/nginx/conf/conf.d/proxy.conf;
    
    # 包含缓存配置
    include /usr/local/openresty/nginx/conf/conf.d/cache.conf;

    # 包含上游配置
    include /usr/local/openresty/nginx/conf/upstream.d/*.conf;
    
    # 包含虚拟主机配置
    include /usr/local/openresty/nginx/conf/vhost.d/*.conf;
}
```

#### 3.4.2 创建日志格式配置 (logging.conf)

```bash
sudo vi /usr/local/openresty/nginx/conf/conf.d/logging.conf
```

添加以下内容：

```nginx
# 定义 realip 变量
map $http_x_forwarded_for $realip {
    default   $remote_addr;
    ~^(?P<firstAddr>[0-9\.]+),?.*$  $firstAddr;
}

# 日志格式定义
log_format detailed escape=json '{
    "timestamp":"$time_iso8601",
    "remote_addr":"$realip",
    "remote_user":"$remote_user",
    "request":"$request",
    "request_method":"$request_method",
    "request_uri":"$request_uri",
    "uri":"$uri",
    "query_string":"$query_string",
    "request_length":"$request_length",
    "status":"$status",
    "bytes_sent":"$bytes_sent",
    "body_bytes_sent":"$body_bytes_sent",
    "http_referer":"$http_referer",
    "http_user_agent":"$http_user_agent",
    "http_x_forwarded_for":"$http_x_forwarded_for",
    "http_x_real_ip":"$http_x_real_ip",
    "upstream_addr":"$upstream_addr",
    "upstream_response_time":"$upstream_response_time",
    "upstream_connect_time":"$upstream_connect_time",
    "upstream_header_time":"$upstream_header_time",
    "upstream_status":"$upstream_status",
    "request_time":"$request_time",
    "ssl_protocol":"$ssl_protocol",
    "ssl_cipher":"$ssl_cipher",
    "scheme":"$scheme",
    "server_protocol":"$server_protocol",
    "pipe":"$pipe",
    "gzip_ratio":"$gzip_ratio",
    "http_host":"$http_host"
}';

# 访问日志配置
access_log /usr/local/openresty/nginx/logs/access.log detailed 
    buffer=64k
    flush=5s
    gzip=1;

# 错误日志级别
error_log /usr/local/openresty/nginx/logs/error.log error;
```

#### 3.4.3 创建示例虚拟主机配置

```bash
sudo vi /usr/local/openresty/nginx/conf/vhost.d/default.conf
```

添加以下内容：

```nginx
# 请求 ID 处理
map $http_stgw_request_id $trace_id {
    default   $http_stgw_request_id;
    ""        $request_id;
}

server {
    listen 80 default_server;
    server_name _;
    
    # 日志配置
    access_log /usr/local/openresty/nginx/logs/default-access.log detailed;
    error_log /usr/local/openresty/nginx/logs/default-error.log error;
    
    # 根目录配置
    root /usr/local/openresty/nginx/html;
    index index.html index.htm;
    
    # 请求 ID 透传
    proxy_set_header stgw_request_id $trace_id;
    add_header stgw_request_id $trace_id always;
    
    # 健康检查
    location = /health {
        access_log off;
        add_header Content-Type text/plain;
        return 200 "OK\n";
    }
    
    # 默认处理
    location / {
        try_files $uri $uri/ =404;
    }
}
```

### 3.5 测试配置并启动 OpenResty

```bash
# 测试配置
sudo /usr/local/openresty/nginx/sbin/nginx -t

# 启动 OpenResty
sudo /usr/local/openresty/nginx/sbin/nginx

# 查看 Nginx 状态
ps aux | grep nginx
```

## 4. 设置开机启动

### 4.1 创建 Systemd 服务文件

```bash
sudo vi /etc/systemd/system/openresty.service
```

添加以下内容：

```ini
[Unit]
Description=The OpenResty Web Platform
After=network.target

[Service]
Type=forking
PIDFile=/usr/local/openresty/nginx/logs/nginx.pid
ExecStartPre=/usr/local/openresty/nginx/sbin/nginx -t -q -g 'daemon on; master_process on;'
ExecStart=/usr/local/openresty/nginx/sbin/nginx -g 'daemon on; master_process on;'
ExecReload=/usr/local/openresty/nginx/sbin/nginx -g 'daemon on; master_process on;' -s reload
ExecStop=/usr/local/openresty/nginx/sbin/nginx -s quit
TimeoutStopSec=5
KillMode=mixed

# 资源限制
LimitNOFILE=1048576
LimitNPROC=1048576
LimitCORE=infinity

# 安全限制
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
ReadOnlyDirectories=/etc
ReadWriteDirectories=/usr/local/openresty/nginx/logs /var/log/nginx /usr/local/openresty/nginx/cache

# 重启策略
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
```

### 4.2 启用并启动服务

```bash
sudo systemctl daemon-reload
sudo systemctl enable openresty
sudo systemctl start openresty
```

## 5. 系统优化

### 5.1 调整系统限制

编辑 `/etc/security/limits.conf` 文件：

```bash
sudo vi /etc/security/limits.conf
```

添加以下内容：

```
# 系统全局限制
* soft nofile 1048576
* hard nofile 1048576
* soft nproc 1048576
* hard nproc 1048576

# Nginx 用户限制
nginx soft nofile 1048576
nginx hard nofile 1048576
nginx soft nproc 1048576
nginx hard nproc 1048576
```

### 5.2 调整内核参数

编辑 `/etc/sysctl.conf` 文件：

```bash
sudo vi /etc/sysctl.conf
```

添加以下内容：

```
# 网络连接优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 65535
net.core.rmem_default = 16777216
net.core.wmem_default = 16777216
net.core.rmem_max = 33554432
net.core.wmem_max = 33554432

# TCP 优化
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_max_tw_buckets = 1048576
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15

# TCP BBR 拥塞控制
net.core.default_qdisc = fq
net.ipv4.tcp_congestion_control = bbr

# 内存管理优化
vm.swappiness = 0
vm.dirty_ratio = 40
vm.dirty_background_ratio = 10
```

应用新的内核参数：

```bash
sudo sysctl -p
```

## 6. 常用操作命令

### 6.1 服务管理

```bash
# 启动 OpenResty
sudo systemctl start openresty

# 停止 OpenResty
sudo systemctl stop openresty

# 重启 OpenResty
sudo systemctl restart openresty

# 重新加载配置
sudo systemctl reload openresty

# 查看服务状态
sudo systemctl status openresty
```

### 6.2 Nginx 命令

```bash
# 添加 Nginx 命令到 PATH
echo 'export PATH=$PATH:/usr/local/openresty/nginx/sbin' >> ~/.bashrc
source ~/.bashrc

# 测试配置
nginx -t

# 重新加载配置
nginx -s reload

# 优雅停止
nginx -s quit

# 立即停止
nginx -s stop

# 查看编译选项和模块
nginx -V
```

## 7. 高级配置示例

### 7.1 负载均衡配置

创建上游服务器配置文件：

```bash
sudo vi /usr/local/openresty/nginx/conf/upstream.d/backend.conf
```

添加以下内容：

```nginx
upstream backend {
    # 使用一致性哈希
    hash $request_uri consistent;
    
    server backend1.example.com:8080 weight=5 max_fails=3 fail_timeout=30s;
    server backend2.example.com:8080 weight=5 max_fails=3 fail_timeout=30s;
    
    keepalive 256;
    keepalive_requests 10000;
    keepalive_timeout 60s;
}
```

### 7.2 HTTPS 配置

创建 SSL 配置文件：

```bash
sudo vi /usr/local/openresty/nginx/conf/conf.d/ssl.conf
```

添加以下内容：

```nginx
# SSL 配置
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305;
ssl_session_cache shared:SSL:50m;
ssl_session_timeout 1d;
ssl_session_tickets off;
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;
```

### 7.3 安全相关配置

创建安全头部配置文件：

```bash
sudo vi /usr/local/openresty/nginx/conf/conf.d/security_headers.conf
```

添加以下内容：

```nginx
# 安全相关头部
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-Download-Options "noopen" always;
add_header X-Permitted-Cross-Domain-Policies "none" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval'; frame-ancestors 'self';" always;
```

## 8. 故障排查

### 8.1 常见错误及解决方案

#### 8.1.1 权限问题

如果遇到权限相关错误，可能是因为：

1. 日志目录或缓存目录权限不正确
2. 配置文件权限不正确

解决方案：
```bash
# 修复权限
sudo chown -R nginx:nginx /usr/local/openresty/nginx/logs
sudo chown -R nginx:nginx /usr/local/openresty/nginx/cache
sudo chown -R nginx:nginx /var/log/nginx
```

#### 8.1.2 配置语法错误

如果遇到配置语法错误，使用以下命令检查：

```bash
nginx -t
```

### 8.2 查看日志

```bash
# 查看错误日志
tail -f /usr/local/openresty/nginx/logs/error.log

# 查看访问日志
tail -f /usr/local/openresty/nginx/logs/access.log
```

## 9. 性能监控

### 9.1 启用 Nginx 状态监控

编辑默认虚拟主机配置，添加状态监控：

```nginx
location = /nginx_status {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    deny all;
}
```

### 9.2 使用 ngxtop 监控

安装 ngxtop：

```bash
pip install ngxtop
```

使用 ngxtop 监控：

```bash
ngxtop -l /usr/local/openresty/nginx/logs/access.log
```

## 10. 备份与恢复

### 10.1 配置备份

```bash
# 创建备份目录
mkdir -p /backup/openresty/$(date +%Y%m%d)

# 备份配置文件
cp -r /usr/local/openresty/nginx/conf /backup/openresty/$(date +%Y%m%d)/
```

### 10.2 自动备份脚本

创建自动备份脚本：

```bash
vi /usr/local/bin/backup-openresty.sh
```

添加以下内容：

```bash
#!/bin/bash
BACKUP_DIR="/backup/openresty/$(date +%Y%m%d-%H%M%S)"
mkdir -p $BACKUP_DIR
cp -r /usr/local/openresty/nginx/conf $BACKUP_DIR/
find /backup/openresty -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null || true
```

设置执行权限并添加到 crontab：

```bash
chmod +x /usr/local/bin/backup-openresty.sh
echo "0 2 * * * /usr/local/bin/backup-openresty.sh" | crontab -
```

## 11. 更新与升级

### 11.1 升级 OpenResty

```bash
# 下载新版本
cd /usr/local/src
wget https://openresty.org/download/openresty-x.y.z.tar.gz
tar -zxvf openresty-x.y.z.tar.gz
cd openresty-x.y.z

# 配置、编译和安装
./configure --prefix=/usr/local/openresty [其他选项]
make
make install

# 重启服务
systemctl restart openresty
```

## 12. 参考资料

- [OpenResty 官方文档](https://openresty.org/en/documentation.html)
- [Nginx 官方文档](https://nginx.org/en/docs/)

## 13. GeoIP 模块配置与应用

GeoIP 模块是 Nginx/OpenResty 的强大扩展，允许基于客户端 IP 地址获取地理位置信息。本章将详细介绍 GeoIP 模块的安装、配置、应用场景和故障排查。

### 13.1 GeoIP 模块概述

#### 13.1.1 功能与优势

- **地理位置识别**：根据 IP 地址识别访问者的国家、地区、城市等
- **内容本地化**：根据地理位置提供不同的内容或语言
- **访问控制**：限制或允许特定地区的访问
- **流量分析**：在日志中记录地理位置数据，用于分析用户分布
- **防欺诈**：识别可疑地区的流量，加强安全措施

#### 13.1.2 工作原理

GeoIP 模块通过查询 MaxMind 提供的 GeoIP 数据库，将 IP 地址映射到地理位置信息。这个过程在请求处理阶段进行，不会显著影响性能。

### 13.2 安装 GeoIP 依赖和数据库

#### 13.2.1 安装系统依赖

不同系统的安装命令：

**CentOS/RHEL:**
```bash
# 安装 EPEL 仓库（如果尚未安装）
sudo yum install epel-release -y

# 安装 GeoIP 开发库和工具
sudo yum install GeoIP GeoIP-devel GeoIP-data -y
```

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install libgeoip-dev geoip-database -y
```

#### 13.2.2 创建数据目录

```bash
# 创建 GeoIP 数据目录
sudo mkdir -p /usr/share/GeoIP
cd /usr/share/GeoIP
```

#### 13.2.3 下载 MaxMind GeoIP 数据库

MaxMind 提供了免费的 GeoLite2 数据库和付费的 GeoIP2 数据库。以下是获取免费数据库的方法：

```bash
# 下载国家数据库
sudo wget https://dl.miyuru.lk/geoip/maxmind/country/maxmind.dat.gz -O GeoIP.dat.gz
sudo gunzip -f GeoIP.dat.gz

# 下载城市数据库
sudo wget https://dl.miyuru.lk/geoip/maxmind/city/maxmind.dat.gz -O GeoLiteCity.dat.gz
sudo gunzip -f GeoLiteCity.dat.gz

# 下载 ASN 数据库（可选，用于识别 ISP）
sudo wget https://dl.miyuru.lk/geoip/maxmind/asn/maxmind.dat.gz -O GeoIPASNum.dat.gz
sudo gunzip -f GeoIPASNum.dat.gz

# 设置适当的权限
sudo chmod 644 /usr/share/GeoIP/*.dat
```

#### 13.2.4 使用 MaxMind GeoIP2 数据库（商业版，可选）

如果您使用付费的 GeoIP2 数据库，需要先注册 MaxMind 账户并获取许可证密钥：

```bash
# 安装 MaxMind 工具
sudo yum install geoipupdate -y  # CentOS
# 或
sudo apt-get install geoipupdate -y  # Ubuntu

# 配置 MaxMind 账户
sudo vi /etc/GeoIP.conf
```

添加以下内容（替换为您的账户信息）：
```
AccountID 12345
LicenseKey YOUR_LICENSE_KEY
EditionIDs GeoLite2-Country GeoLite2-City GeoLite2-ASN
```

更新数据库：
```bash
sudo geoipupdate
```

### 13.3 重新编译 OpenResty 以支持 GeoIP

#### 13.3.1 备份当前安装

```bash
# 备份当前 OpenResty 安装
sudo cp -r /usr/local/openresty /usr/local/openresty.bak.$(date +%Y%m%d)

# 备份配置文件
sudo cp -r /usr/local/openresty/nginx/conf /usr/local/openresty/nginx/conf.bak.$(date +%Y%m%d)
```

#### 13.3.2 获取当前编译选项

在重新编译前，获取当前的编译选项是个好习惯：

```bash
/usr/local/openresty/nginx/sbin/nginx -V
```

记录输出的编译选项，确保在新的编译中包含这些选项。

#### 13.3.3 编译 OpenResty 与 GeoIP 模块

```bash
# 进入 OpenResty 源码目录
cd /usr/local/src/openresty-********

# 配置编译选项，添加 GeoIP 模块
sudo ./configure \
  --prefix=/usr/local/openresty \
  --with-cc-opt='-O2 -I/usr/local/openresty/zlib/include -I/usr/local/openresty/pcre/include -I/usr/local/openresty/openssl111/include' \
  --with-ld-opt='-Wl,-rpath,/usr/local/lib' \
  --with-cc='ccache gcc -fdiagnostics-color=always' \
  --with-pcre-jit \
  --with-http_ssl_module \
  --with-http_realip_module \
  --with-http_addition_module \
  --with-http_sub_module \
  --with-http_dav_module \
  --with-http_flv_module \
  --with-http_mp4_module \
  --with-http_gunzip_module \
  --with-http_gzip_static_module \
  --with-http_auth_request_module \
  --with-http_random_index_module \
  --with-http_secure_link_module \
  --with-http_stub_status_module \
  --with-http_v2_module \
  --with-http_slice_module \
  --with-stream \
  --with-stream_ssl_module \
  --with-stream_ssl_preread_module \
  --with-threads \
  --with-http_geoip_module  # 添加 GeoIP 模块

# 编译
sudo make -j$(nproc)

# 安装
sudo make install
```

#### 13.3.4 验证 GeoIP 模块安装

```bash
# 检查 GeoIP 模块是否已编译
/usr/local/openresty/nginx/sbin/nginx -V 2>&1 | grep --color geoip

# 应该显示 --with-http_geoip_module
```

### 13.4 GeoIP 基础配置

#### 13.4.1 创建 GeoIP 配置文件

```bash
sudo mkdir -p /usr/local/openresty/nginx/conf/conf.d
sudo vi /usr/local/openresty/nginx/conf/conf.d/geoip.conf
```

添加以下基础配置：

```nginx
# GeoIP 数据库路径配置
geoip_country /usr/share/GeoIP/GeoIP.dat;
geoip_city /usr/share/GeoIP/GeoLiteCity.dat;

# 可选：配置 ASN 数据库
# geoip_org /usr/share/GeoIP/GeoIPASNum.dat;

# 国家代码到国家名称的映射
map $geoip_country_code $geoip_country_name {
    default "Unknown";
    "" "Unknown";
    A1 "Anonymous Proxy";
    A2 "Satellite Provider";
    O1 "Other Country";
    AD "Andorra";
    AE "United Arab Emirates";
    AF "Afghanistan";
    AG "Antigua and Barbuda";
    AI "Anguilla";
    AL "Albania";
    AM "Armenia";
    AO "Angola";
    AP "Asia/Pacific Region";
    AQ "Antarctica";
    AR "Argentina";
    AS "American Samoa";
    AT "Austria";
    AU "Australia";
    AW "Aruba";
    AX "Aland Islands";
    AZ "Azerbaijan";
    BA "Bosnia and Herzegovina";
    BB "Barbados";
    BD "Bangladesh";
    BE "Belgium";
    BF "Burkina Faso";
    BG "Bulgaria";
    BH "Bahrain";
    BI "Burundi";
    BJ "Benin";
    BL "Saint Barthelemy";
    BM "Bermuda";
    BN "Brunei Darussalam";
    BO "Bolivia";
    BQ "Bonaire, Saint Eustatius and Saba";
    BR "Brazil";
    BS "Bahamas";
    BT "Bhutan";
    BV "Bouvet Island";
    BW "Botswana";
    BY "Belarus";
    BZ "Belize";
    CA "Canada";
    CC "Cocos (Keeling) Islands";
    CD "Congo, The Democratic Republic of the";
    CF "Central African Republic";
    CG "Congo";
    CH "Switzerland";
    CI "Cote d'Ivoire";
    CK "Cook Islands";
    CL "Chile";
    CM "Cameroon";
    CN "China";
    CO "Colombia";
    CR "Costa Rica";
    CU "Cuba";
    CV "Cape Verde";
    CW "Curacao";
    CX "Christmas Island";
    CY "Cyprus";
    CZ "Czech Republic";
    DE "Germany";
    DJ "Djibouti";
    DK "Denmark";
    DM "Dominica";
    DO "Dominican Republic";
    DZ "Algeria";
    EC "Ecuador";
    EE "Estonia";
    EG "Egypt";
    EH "Western Sahara";
    ER "Eritrea";
    ES "Spain";
    ET "Ethiopia";
    EU "Europe";
    FI "Finland";
    FJ "Fiji";
    FK "Falkland Islands (Malvinas)";
    FM "Micronesia, Federated States of";
    FO "Faroe Islands";
    FR "France";
    GA "Gabon";
    GB "United Kingdom";
    GD "Grenada";
    GE "Georgia";
    GF "French Guiana";
    GG "Guernsey";
    GH "Ghana";
    GI "Gibraltar";
    GL "Greenland";
    GM "Gambia";
    GN "Guinea";
    GP "Guadeloupe";
    GQ "Equatorial Guinea";
    GR "Greece";
    GS "South Georgia and the South Sandwich Islands";
    GT "Guatemala";
    GU "Guam";
    GW "Guinea-Bissau";
    GY "Guyana";
    HK "Hong Kong";
    HM "Heard Island and McDonald Islands
```

### 13.5 在 nginx.conf 中引入 GeoIP 配置

确保在 `http` 块中包含 GeoIP 配置：

```nginx
http {
    include /usr/local/openresty/nginx/conf/mime.types;
    default_type application/octet-stream;
    charset utf-8;
    
    # 包含 GeoIP 配置
    include /usr/local/openresty/nginx/conf/conf.d/geoip.conf;
    
    # 其他配置...
}
```

### 13.6 在日志中使用 GeoIP 变量

修改日志格式以包含 GeoIP 信息：

```nginx
# 日志格式定义
log_format geo_log escape=json '{
    "timestamp":"$time_iso8601",
    "remote_addr":"$realip",
    "country_code":"$geoip_country_code",
    "country_name":"$geoip_country_name",
    "city":"$geoip_city",
    "request":"$request",
    "status":"$status",
    "body_bytes_sent":"$body_bytes_sent",
    "http_referer":"$http_referer",
    "http_user_agent":"$http_user_agent",
    "request_time":"$request_time"
}';

# 使用 GeoIP 日志格式
access_log /usr/local/openresty/nginx/logs/geo-access.log geo_log buffer=64k flush=5s;
```

### 13.7 基于地理位置的访问控制

您可以使用 GeoIP 变量来实现基于地理位置的访问控制：

```nginx
# 仅允许特定国家访问
map $geoip_country_code $allowed_country {
    default 0;
    US 1;
    CN 1;
    JP 1;
}

server {
    # 其他配置...
    
    # 基于国家的访问控制
    if ($allowed_country = 0) {
        return 403 "Access denied by geographic restriction";
    }
    
    # 其他配置...
}
```

### 13.8 基于地理位置的内容定制

您可以根据访问者的地理位置提供不同的内容：

```nginx
# 根据国家重定向到不同的子域名
map $geoip_country_code $country_specific_site {
    default example.com;
    US us.example.com;
    CN cn.example.com;
    JP jp.example.com;
}

server {
    listen 80;
    server_name example.com;
    
    # 重定向到国家特定站点
    if ($geoip_country_code !~ "^$") {
        return 301 https://$country_specific_site$request_uri;
    }
    
    # 其他配置...
}
```

### 13.9 故障排查

如果遇到 GeoIP 相关问题，请检查以下几点：

1. **确认 GeoIP 模块已加载**：
   ```bash
   nginx -V 2>&1 | grep --color geoip
   ```
   应该显示 `--with-http_geoip_module`

2. **检查 GeoIP 数据库文件**：
   ```bash
   ls -la /usr/share/GeoIP/
   ```
   确保文件存在且权限正确

3. **检查配置语法**：
   ```bash
   nginx -t
   ```
   
4. **常见错误**：
   - `unknown "geoip_country_code" variable` - GeoIP 模块未编译或未正确加载
   - `open() "/usr/share/GeoIP/GeoIP.dat" failed` - 数据库文件不存在或权限不正确
   - `the duplicate "geoip_country_name" variable` - 变量在多个位置定义

5. **临时禁用 GeoIP**：
   如果遇到无法解决的问题，可以临时注释掉 GeoIP 配置：
   ```bash
   sudo sed -i 's/include .*geoip.conf;/# include geoip.conf;/' /usr/local/openresty/nginx/conf/nginx.conf
   ```

### 13.10 更新 GeoIP 数据库

GeoIP 数据库应定期更新以保持准确性：

```bash
# 创建更新脚本
sudo vi /usr/local/bin/update-geoip.sh
```

添加以下内容：

```bash
#!/bin/bash
cd /usr/share/GeoIP
wget -q https://dl.miyuru.lk/geoip/maxmind/country/maxmind.dat.gz -O GeoIP.dat.gz.new
wget -q https://dl.miyuru.lk/geoip/maxmind/city/maxmind.dat.gz -O GeoLiteCity.dat.gz.new

if [ -s GeoIP.dat.gz.new ]; then
    gunzip -f GeoIP.dat.gz.new
    mv GeoIP.dat.new GeoIP.dat
    chmod 644 GeoIP.dat
    echo "Country database updated successfully"
else
    echo "Failed to download country database"
fi

if [ -s GeoLiteCity.dat.gz.new ]; then
    gunzip -f GeoLiteCity.dat.gz.new
    mv GeoLiteCity.dat.new GeoLiteCity.dat
    chmod 644 GeoLiteCity.dat
    echo "City database updated successfully"
else
    echo "Failed to download city database"
fi
```

设置执行权限并添加到 crontab：

```bash
sudo chmod +x /usr/local/bin/update-geoip.sh
echo "0 3 1 * * /usr/local/bin/update-geoip.sh" | sudo tee -a /etc/crontab
```

这将在每月 1 日凌晨 3 点更新 GeoIP 数据库。

## 15. 日志管理与切割

### 15.1 配置 logrotate

Logrotate 是 Linux 系统中常用的日志切割工具，可以自动轮转、压缩和删除旧日志文件。

#### 15.1.1 创建 logrotate 配置文件

```bash
sudo vi /etc/logrotate.d/openresty
```

添加以下内容：

```
/usr/local/openresty/nginx/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 640 nginx nginx
    sharedscripts
    postrotate
        [ -f /usr/local/openresty/nginx/logs/nginx.pid ] && kill -USR1 `cat /usr/local/openresty/nginx/logs/nginx.pid`
    endscript
}
```

这个配置的含义：
- `daily`: 每天切割一次日志
- `missingok`: 如果日志文件不存在，不报错
- `rotate 14`: 保留最近 14 天的日志
- `compress`: 压缩旧日志文件
- `delaycompress`: 延迟压缩，直到下一个轮转周期
- `notifempty`: 如果日志文件为空，不进行轮转
- `create 640 nginx nginx`: 创建新日志文件，权限为 640，所有者为 nginx
- `sharedscripts`: 所有匹配的日志文件共享一个脚本
- `postrotate`: 日志轮转后执行的脚本，向 Nginx 发送 USR1 信号，使其重新打开日志文件

#### 15.1.2 测试 logrotate 配置

```bash
sudo logrotate -d /etc/logrotate.d/openresty
```

这将以调试模式运行 logrotate，显示将要执行的操作，但不实际执行。

#### 15.1.3 手动运行 logrotate

```bash
sudo logrotate -f /etc/logrotate.d/openresty
```

这将强制执行日志轮转，无论是否达到轮转条件。

### 15.2 自定义日志格式与切割

#### 15.2.1 按日期命名日志文件

如果需要按日期命名日志文件，可以使用 Nginx 变量：

```nginx
http {
    # 其他配置...
    
    map $time_iso8601 $logdate {
        '~^(?<ymd>\d{4}-\d{2}-\d{2})' $ymd;
        default 'date-error';
    }
    
    # 其他配置...
}

server {
    # 其他配置...
    
    access_log /usr/local/openresty/nginx/logs/access-$logdate.log optimized_log;
    error_log /usr/local/openresty/nginx/logs/error-$logdate.log error;
    
    # 其他配置...
}
```

这样会为每天创建一个新的日志文件，格式为 `access-YYYY-MM-DD.log`。

#### 15.2.2 使用 Lua 脚本进行高级日志管理

对于更复杂的日志管理需求，可以使用 Lua 脚本：

```nginx
http {
    # 其他配置...
    
    lua_shared_dict log_dict 10m;
    
    init_by_lua_block {
        local log_dict = ngx.shared.log_dict
        log_dict:set("counter", 0)
    }
    
    log_by_lua_block {
        local log_dict = ngx.shared.log_dict
        local counter = log_dict:incr("counter", 1)
        
        -- 每 10000 个请求检查一次日志大小
        if counter % 10000 == 0 then
            local file = io.open(ngx.var.access_log_path, "r")
            if file then
                local size = file:seek("end")
                file:close()
                
                -- 如果日志大于 1GB，发送 USR1 信号
                if size > 1073741824 then
                    os.execute("kill -USR1 " .. ngx.var.pid)
                    log_dict:set("counter", 0)
                end
            end
        end
    }
    
    # 其他配置...
}
```

### 15.3 日志分析工具

#### 15.3.1 安装 GoAccess 实时日志分析器

```bash
# 安装依赖
sudo yum install ncurses-devel geoip-devel -y

# 安装 GoAccess
cd /usr/local/src
sudo wget https://tar.goaccess.io/goaccess-1.6.2.tar.gz
sudo tar -xzvf goaccess-1.6.2.tar.gz
cd goaccess-1.6.2
sudo ./configure --enable-utf8 --enable-geoip=legacy
sudo make
sudo make install
```

#### 15.3.2 使用 GoAccess 分析日志

```bash
# 实时分析日志
goaccess /usr/local/openresty/nginx/logs/access.log -c

# 生成 HTML 报告
goaccess /usr/local/openresty/nginx/logs/access.log -o /var/www/html/report.html --log-format=COMBINED
```

#### 15.3.3 配置 Nginx 提供 GoAccess 报告访问

```nginx
server {
    listen 8080;
    server_name localhost;
    
    location /stats {
        alias /var/www/html;
        index report.html;
        
        auth_basic "Restricted Access";
        auth_basic_user_file /usr/local/openresty/nginx/conf/.htpasswd;
    }
}
```

创建用户名和密码：

```bash
sudo yum install httpd-tools -y
sudo htpasswd -c /usr/local/openresty/nginx/conf/.htpasswd admin
```

### 15.4 日志安全与合规

#### 15.4.1 敏感信息处理

配置 Nginx 在日志中隐藏敏感信息：

```nginx
# 在 http 块中定义
map $request_uri $filtered_uri {
    ~^(?<no_query>.+)\?(?<query>.+) $no_query;
    default $request_uri;
}

# 在 server 块中使用
set $logged_uri $filtered_uri;

# 替换信息
if ($request_uri ~ "password=([^&]+)") {
    set $logged_uri $filtered_uri?password=REDACTED;
}

# 使用过滤后的 URI
access_log /usr/local/openresty/nginx/logs/access.log combined_filtered;
```

#### 15.4.2 日志加密与备份

设置日志加密备份脚本：

```bash
sudo vi /usr/local/bin/encrypt-logs.sh
```

添加以下内容：

```bash
#!/bin/bash
YESTERDAY=$(date -d "yesterday" +%Y-%m-%d)
LOG_DIR="/usr/local/openresty/nginx/logs"
BACKUP_DIR="/backup/logs"
ENCRYPTION_KEY="/etc/ssl/private/log-encryption.key"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 压缩昨天的日志
find $LOG_DIR -name "*$YESTERDAY*" -type f -exec gzip {} \;

# 加密并移动到备份目录
find $LOG_DIR -name "*$YESTERDAY*.gz" -type f -exec openssl enc -aes-256-cbc -salt -in {} -out $BACKUP_DIR/{}.enc -pass file:$ENCRYPTION_KEY \; -exec rm {} \;

# 删除 30 天前的备份
find $BACKUP_DIR -type f -mtime +30 -delete
```

设置执行权限并添加到 crontab：

```bash
sudo chmod +x /usr/local/bin/encrypt-logs.sh
echo "0 1 * * * /usr/local/bin/encrypt-logs.sh" | sudo tee -a /etc/crontab
```

生成加密密钥：

```bash
sudo openssl rand -out /etc/ssl/private/log-encryption.key 32
sudo chmod 400 /etc/ssl/private/log-encryption.key
```


