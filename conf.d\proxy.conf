# 代理缓冲区设置
proxy_buffers 32 32k;                  
proxy_buffer_size 32k;
proxy_cache_path /usr/local/openresty/nginx/cache/nginx levels=1:2 
    keys_zone=nginx_cache:10m 
    max_size=10g 
    inactive=60m 
    use_temp_path=off;

# 添加缓存配置
proxy_cache nginx_cache;
proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
proxy_cache_background_update on;
proxy_cache_lock_timeout 5s;

# 代理重试设置
proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
proxy_next_upstream_tries 3;
proxy_next_upstream_timeout 10s;

# 开启 keepalive
proxy_http_version 1.1;
proxy_set_header Connection "";

# WebSocket 支持
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection $connection_upgrade;