- job_name: 'OPSClusterKubeApiserver'
  scheme: https
  bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
  tls_config:
    insecure_skip_verify: true
  kubernetes_sd_configs:
    - role: endpoints
  relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name]
      action: keep
      regex: default;kubernetes
    - source_labels: [__meta_kubernetes_endpoint_port_name]
      action: keep
      regex: https
    # 移除这个端口过滤或修改为443
    # - source_labels: [__meta_kubernetes_endpoint_port]
    #   action: keep
    #   regex: 60002