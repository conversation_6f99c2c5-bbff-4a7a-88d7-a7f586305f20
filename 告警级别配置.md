# 自动化监控告警系统配置指南

## 告警级别配置

| 告警级别 | 通知方式 | 说明 | 响应时间 |
|---------|---------|------|---------|
| 1级告警 | 电话 + 小闪 + 小闪群 | 最高优先级，需要立即处理的严重问题 | 5分钟内 |
| 2级告警 | 小闪 + 小闪群 | 中等优先级，需要及时关注的问题 | 30分钟内 |
| 3级告警 | 小闪群 | 低优先级，需要团队知晓的问题 | 2小时内 |

## 告警级别详细说明

### 1级告警（严重 - Critical）
**触发条件:**
- 服务完全不可用（up == 0）
- Kubernetes API Server宕机
- ETCD集群节点不可用
- 节点完全宕机
- 内存使用率 > 90%
- 磁盘空间使用率 > 90%

**通知方式:**
- 电话告警（通过webhook调用电话接口）
- Slack个人消息（@oncall-engineer）
- Slack群组消息（#ops-alerts，@channel）
- 邮件通知

**响应要求:**
- 立即响应（5分钟内）
- 需要立即处理
- 可能需要唤醒值班人员

### 2级告警（警告 - Warning）
**触发条件:**
- CPU使用率 > 80%
- 内存使用率 > 75%
- 磁盘空间使用率 > 80%
- 网络连接数 > 10000
- Nginx 5xx错误率 > 5%
- Pod重启频繁（1小时内 > 5次）
- 文件描述符使用率 > 80%
- 网络错误率过高

**通知方式:**
- Slack个人消息（@oncall-engineer）
- Slack群组消息（#ops-alerts）

**响应要求:**
- 及时响应（30分钟内）
- 需要关注和处理
- 防止问题升级

### 3级告警（信息 - Info）
**触发条件:**
- CPU使用率 > 60%
- 内存使用率 > 60%
- 磁盘空间使用率 > 70%
- 负载均衡器响应时间 > 2秒
- SSL证书即将过期（30天内）
- 活跃连接数 > 1000

**通知方式:**
- Slack群组消息（#ops-alerts）

**响应要求:**
- 定期检查（2小时内）
- 需要团队知晓
- 预防性维护

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    VMAgent      │───▶│  VictoriaMetrics│───▶│     VMAlert     │
│   (数据采集)     │    │    (数据存储)    │    │   (告警引擎)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   通知渠道       │◀───│  AlertManager   │◀───│   告警规则       │
│ (Slack/邮件等)   │    │   (告警管理)     │    │  (规则配置)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 监控目标

### Kubernetes集群监控
- **API Server**: 集群管理功能监控
- **Nodes**: 节点状态和资源监控
- **ETCD**: 集群数据存储监控
- **CoreDNS**: 集群DNS服务监控

### 应用服务监控
- **Nginx**: Web服务器性能和错误监控
- **应用Pod**: 容器状态和重启监控
- **存储**: PVC状态监控

### 基础设施监控
- **CPU**: 使用率和负载监控
- **内存**: 使用率和可用性监控
- **磁盘**: 空间使用和IO监控
- **网络**: 连接数和错误率监控

## 部署步骤

### 1. 环境准备
```bash
# 检查kubectl连接
kubectl cluster-info

# 创建命名空间
kubectl create namespace monitoring
```

### 2. 配置验证
```bash
# 验证配置文件
chmod +x validate-config.sh
./validate-config.sh
```

### 3. 自动化部署
```bash
# 执行部署脚本
chmod +x deploy-monitoring.sh
./deploy-monitoring.sh
```

### 4. 功能测试
```bash
# 测试告警功能
chmod +x test-alerts.sh
./test-alerts.sh --test
```

## 配置文件说明

### 1. vmagent-cr.yaml
- VMAgent配置，负责数据采集
- 配置各种监控目标的抓取规则
- 设置远程写入地址

### 2. alertmanager-config.yaml
- AlertManager配置和部署
- 配置告警路由规则
- 设置通知接收器

### 3. alert-rules.yaml
- 告警规则定义
- 按级别分组的告警条件
- 告警阈值和持续时间

### 4. vmalert-config.yaml
- VMAlert配置和部署
- 告警规则引擎配置
- 数据源和通知目标配置

### 5. notification-templates.yaml
- 通知模板配置
- 支持多种通知方式
- 自定义消息格式

## 自定义配置

### 修改通知方式
1. 编辑 `alertmanager-config.yaml`
2. 更新Webhook URL
3. 配置SMTP服务器信息
4. 重新部署配置

### 调整告警阈值
1. 编辑 `alert-rules.yaml`
2. 修改告警条件和阈值
3. 重新应用配置

### 添加新的监控目标
1. 编辑 `vmagent-cr.yaml`
2. 添加新的scrapeConfigs
3. 重新部署VMAgent

## 故障排查

### 常见问题
1. **告警不触发**: 检查告警规则语法和数据源
2. **通知不发送**: 验证Webhook URL和网络连接
3. **数据采集失败**: 检查目标服务的metrics端点
4. **资源不足**: 调整资源限制和节点容量

### 日志查看
```bash
# 查看VMAgent日志
kubectl logs -n monitoring deployment/vmagent-monitoring

# 查看VMAlert日志
kubectl logs -n monitoring deployment/vmalert-monitoring

# 查看AlertManager日志
kubectl logs -n monitoring deployment/alertmanager
```

### 调试命令
```bash
# 检查服务状态
kubectl get pods -n monitoring

# 查看告警规则
kubectl get vmrule -n monitoring

# 端口转发访问UI
./port-forward.sh
```

## 维护建议

1. **定期测试**: 每月执行一次告警测试
2. **规则优化**: 根据实际情况调整告警阈值
3. **通知验证**: 确保通知渠道正常工作
4. **文档更新**: 及时更新配置文档
5. **备份配置**: 定期备份配置文件
6. **监控监控**: 监控监控系统本身的健康状态

## 扩展功能

### 集成更多通知方式
- 企业微信
- 钉钉机器人
- 短信通知
- PagerDuty

### 添加更多监控指标
- 业务指标监控
- 自定义应用指标
- 外部服务监控
- 网络质量监控

### 高级功能
- 告警抑制规则
- 告警静默管理
- 告警升级策略
- 多集群监控