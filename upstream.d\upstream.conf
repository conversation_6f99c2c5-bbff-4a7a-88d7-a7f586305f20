# 上游服务器配置
upstream backend {
    # 需要会话保持或缓存一致性 没有类似场景注释掉使用轮询即可
    hash $cookie_sessionid consistent;  # 会话保持
    hash $request_uri consistent;  # 使用一致性哈希
    
    server backend1.example.com:8080 weight=5 max_fails=3 fail_timeout=30s;
    server backend2.example.com:8080 weight=5 max_fails=3 fail_timeout=30s;
    
    keepalive 256;                # 增加空闲keepalive连接数
    keepalive_requests 10000;     # 增加每个连接的请求数
    keepalive_timeout 60s;
}
