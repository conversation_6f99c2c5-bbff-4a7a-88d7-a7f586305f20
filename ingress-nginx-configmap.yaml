apiVersion: v1
kind: ConfigMap
metadata:
  name: ingress-nginx-controller
  namespace: ingress-nginx
data:
  # 基本配置
  enable-access-log: "true"
  access-log-path: "/var/log/nginx/access.log"
  error-log-path: "/var/log/nginx/error.log"
  
  # 日志格式配置 - 与 Nginx detailed 格式保持一致
  log-format-upstream: '{"timestamp":"$time_iso8601","remote_addr":"$remote_addr","remote_user":"$remote_user","request":"$request","request_method":"$request_method","request_uri":"$request_uri","uri":"$uri","query_string":"$query_string","request_length":"$request_length","status":"$status","bytes_sent":"$bytes_sent","body_bytes_sent":"$body_bytes_sent","http_referer":"$http_referer","http_user_agent":"$http_user_agent","http_x_forwarded_for":"$http_x_forwarded_for","http_x_real_ip":"$http_x_real_ip","request_id":"$request_id","upstream_addr":"$upstream_addr","upstream_response_time":"$upstream_response_time","upstream_connect_time":"$upstream_connect_time","upstream_header_time":"$upstream_header_time","upstream_status":"$upstream_status","request_time":"$request_time","ssl_protocol":"$ssl_protocol","ssl_cipher":"$ssl_cipher","scheme":"$scheme","server_protocol":"$server_protocol","pipe":"$pipe","http_host":"$http_host"}'