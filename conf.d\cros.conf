# CORS 精细化配置
add_header 'Access-Control-Allow-Origin' '$http_origin' always;
add_header 'Access-Control-Allow-Credentials' 'true' always;
add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With,stgw_request_id' always;

# OPTIONS 请求处理
if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Max-Age' 1728000;
    add_header 'Content-Type' 'text/plain charset=UTF-8';
    add_header 'Content-Length' 0;
    return 204;
}


# 允许所有 
#add_header Access-Control-Allow-Origin *;
#add_header Access-Control-Allow-Methods *;
#add_header Access-Control-Allow-Credentials true;
#add_header Access-Control-Allow-Headers *;
#add_header Cache-Control no-store;
 
#if ($request_method = 'OPTIONS') {
#    return 204;
#}