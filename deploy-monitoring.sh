#!/bin/bash

# 自动化监控告警部署脚本
# 作者: 运维团队
# 版本: 1.0
# 描述: 自动部署VictoriaMetrics监控和告警系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装，请先安装kubectl"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建命名空间
create_namespace() {
    log_info "创建monitoring命名空间..."
    
    if kubectl get namespace monitoring &> /dev/null; then
        log_warning "命名空间monitoring已存在"
    else
        kubectl create namespace monitoring
        log_success "命名空间monitoring创建成功"
    fi
}

# 部署VictoriaMetrics Operator
deploy_vm_operator() {
    log_info "部署VictoriaMetrics Operator..."
    
    # 检查是否已安装
    if kubectl get deployment vm-operator -n monitoring &> /dev/null; then
        log_warning "VictoriaMetrics Operator已存在"
        return
    fi
    
    # 安装Operator
    kubectl apply -f https://github.com/VictoriaMetrics/operator/releases/latest/download/bundle_crd.yaml
    kubectl apply -f https://github.com/VictoriaMetrics/operator/releases/latest/download/operator.yaml
    
    # 等待Operator就绪
    log_info "等待VictoriaMetrics Operator就绪..."
    kubectl wait --for=condition=available --timeout=300s deployment/vm-operator -n monitoring
    
    log_success "VictoriaMetrics Operator部署成功"
}

# 部署VictoriaMetrics集群
deploy_vm_cluster() {
    log_info "部署VictoriaMetrics集群..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: operator.victoriametrics.com/v1beta1
kind: VMCluster
metadata:
  name: vm-cluster
  namespace: monitoring
spec:
  retentionPeriod: "30d"
  vmselect:
    replicaCount: 2
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
  vminsert:
    replicaCount: 2
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
  vmstorage:
    replicaCount: 3
    resources:
      requests:
        memory: "1Gi"
        cpu: "300m"
      limits:
        memory: "2Gi"
        cpu: "1"
    storage:
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 50Gi
EOF
    
    log_success "VictoriaMetrics集群配置已应用"
}

# 部署配置文件
deploy_configs() {
    log_info "部署配置文件..."
    
    # 部署告警规则
    if [ -f "alert-rules.yaml" ]; then
        kubectl apply -f alert-rules.yaml
        log_success "告警规则配置已应用"
    else
        log_error "alert-rules.yaml文件不存在"
        exit 1
    fi
    
    # 部署通知模板
    if [ -f "notification-templates.yaml" ]; then
        kubectl apply -f notification-templates.yaml
        log_success "通知模板配置已应用"
    else
        log_error "notification-templates.yaml文件不存在"
        exit 1
    fi
    
    # 部署AlertManager
    if [ -f "alertmanager-config.yaml" ]; then
        kubectl apply -f alertmanager-config.yaml
        log_success "AlertManager配置已应用"
    else
        log_error "alertmanager-config.yaml文件不存在"
        exit 1
    fi
    
    # 部署VMAlert
    if [ -f "vmalert-config.yaml" ]; then
        kubectl apply -f vmalert-config.yaml
        log_success "VMAlert配置已应用"
    else
        log_error "vmalert-config.yaml文件不存在"
        exit 1
    fi
    
    # 部署VMAgent
    if [ -f "vmagent-cr.yaml" ]; then
        kubectl apply -f vmagent-cr.yaml
        log_success "VMAgent配置已应用"
    else
        log_error "vmagent-cr.yaml文件不存在"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待VMCluster就绪
    log_info "等待VictoriaMetrics集群就绪..."
    kubectl wait --for=condition=available --timeout=600s vmcluster/vm-cluster -n monitoring
    
    # 等待AlertManager就绪
    log_info "等待AlertManager就绪..."
    kubectl wait --for=condition=available --timeout=300s deployment/alertmanager -n monitoring
    
    # 等待VMAlert就绪
    log_info "等待VMAlert就绪..."
    kubectl wait --for=condition=available --timeout=300s deployment/vmalert-monitoring -n monitoring
    
    # 等待VMAgent就绪
    log_info "等待VMAgent就绪..."
    kubectl wait --for=condition=available --timeout=300s deployment/vmagent-monitoring -n monitoring
    
    log_success "所有服务已就绪"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n monitoring
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get svc -n monitoring
    
    # 检查告警规则
    log_info "检查告警规则..."
    kubectl get vmrule -n monitoring
    
    log_success "部署验证完成"
}

# 创建端口转发脚本
create_port_forward_script() {
    log_info "创建端口转发脚本..."
    
    cat > port-forward.sh << 'EOF'
#!/bin/bash

# 端口转发脚本
echo "启动端口转发..."

# VictoriaMetrics UI
kubectl port-forward -n monitoring svc/vmselect-vm-cluster 8481:8481 &
echo "VictoriaMetrics UI: http://localhost:8481"

# AlertManager UI
kubectl port-forward -n monitoring svc/alertmanager 9093:9093 &
echo "AlertManager UI: http://localhost:9093"

# VMAlert UI
kubectl port-forward -n monitoring svc/vmalert 8080:8080 &
echo "VMAlert UI: http://localhost:8080"

echo "端口转发已启动，按Ctrl+C停止"
wait
EOF
    
    chmod +x port-forward.sh
    log_success "端口转发脚本已创建: ./port-forward.sh"
}

# 显示访问信息
show_access_info() {
    log_info "部署完成！访问信息："
    echo ""
    echo "🔍 VictoriaMetrics UI:"
    echo "   kubectl port-forward -n monitoring svc/vmselect-vm-cluster 8481:8481"
    echo "   然后访问: http://localhost:8481"
    echo ""
    echo "🚨 AlertManager UI:"
    echo "   kubectl port-forward -n monitoring svc/alertmanager 9093:9093"
    echo "   然后访问: http://localhost:9093"
    echo ""
    echo "📊 VMAlert UI:"
    echo "   kubectl port-forward -n monitoring svc/vmalert 8080:8080"
    echo "   然后访问: http://localhost:8080"
    echo ""
    echo "📝 查看告警规则:"
    echo "   kubectl get vmrule -n monitoring"
    echo ""
    echo "📋 查看Pod状态:"
    echo "   kubectl get pods -n monitoring"
    echo ""
    echo "🔧 使用端口转发脚本:"
    echo "   ./port-forward.sh"
    echo ""
}

# 主函数
main() {
    log_info "开始部署VictoriaMetrics监控告警系统..."
    
    check_dependencies
    create_namespace
    deploy_vm_operator
    deploy_vm_cluster
    deploy_configs
    wait_for_services
    verify_deployment
    create_port_forward_script
    show_access_info
    
    log_success "部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
