# FastCGI 缓存配置
fastcgi_cache_path /usr/local/openresty/nginx/cache/fcgi levels=1:2 
    keys_zone=fcgi_cache:10m 
    max_size=10g 
    inactive=60m 
    use_temp_path=off;
fastcgi_cache_key "$request_method$request_uri";
fastcgi_cache_valid 200 60m;

# 使用过期缓存的条件 - 避免重复值
fastcgi_cache_use_stale error timeout invalid_header http_500;

# 代理缓存配置
proxy_cache_path /usr/local/openresty/nginx/cache/proxy levels=1:2 
    keys_zone=proxy_cache:10m 
    max_size=10g 
    inactive=60m 
    use_temp_path=off;
proxy_cache_key "$scheme$request_method$host$request_uri";
proxy_cache_valid 200 301 302 1h;
proxy_cache_valid 404 1m;
