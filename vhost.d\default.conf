# 主服务配置
server {
    listen 80 default_server reuseport;
    listen [::]:80 default_server reuseport;
    
    # HTTPS 配置
    listen 443 ssl http2 default_server reuseport;
    listen [::]:443 ssl http2 default_server reuseport;
    
    server_name example.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/example.com.key;
    ssl_dhparam /etc/nginx/ssl/dhparam.pem;
    ssl_early_data on;                    # 开启 TLS 1.3 early data
    ssl_session_timeout 1d;               # SSL会话缓存时间
    ssl_buffer_size 4k;                   # SSL缓冲区大小

    # 强制 HTTPS
    if ($scheme != "https") {
        return 301 https://$host$request_uri;
    }

    # 根目录配置
    root /var/www/html;
    index index.html index.htm index.php;

    # 访问日志
    access_log /var/log/nginx/${server_name}/{server_name}-`date +%Y-%m-%d`-access.log detailed buffer=32k flush=1s;
    error_log /var/log/nginx/{server_name}/{server_name}-`date +%Y-%m-%d`-error.log notice;

    # 包含通用配置
    include /etc/nginx/conf.d/general/*.conf;
    
    # API 处理
    include /etc/nginx/conf.d/locations/demo_api.conf;
    
    # 静态文件处理 可选
    include /etc/nginx/conf.d/locations/static.conf;
    
    # 安全头部配置 可选
    include /etc/nginx/conf.d/security_headers.conf;

    # 根据真实upstream配置补充
     proxy_pass http://server_backend;
}
