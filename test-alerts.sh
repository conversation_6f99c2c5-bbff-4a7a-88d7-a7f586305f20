#!/bin/bash

# 告警测试脚本
# 作者: 运维团队
# 版本: 1.0
# 描述: 测试监控告警系统的功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_services() {
    log_info "检查监控服务状态..."
    
    # 检查VMAgent
    if kubectl get deployment vmagent-monitoring -n monitoring &> /dev/null; then
        local vmagent_ready=$(kubectl get deployment vmagent-monitoring -n monitoring -o jsonpath='{.status.readyReplicas}')
        local vmagent_desired=$(kubectl get deployment vmagent-monitoring -n monitoring -o jsonpath='{.spec.replicas}')
        if [ "$vmagent_ready" = "$vmagent_desired" ]; then
            log_success "VMAgent运行正常 ($vmagent_ready/$vmagent_desired)"
        else
            log_error "VMAgent未就绪 ($vmagent_ready/$vmagent_desired)"
            return 1
        fi
    else
        log_error "VMAgent未部署"
        return 1
    fi
    
    # 检查VMAlert
    if kubectl get deployment vmalert-monitoring -n monitoring &> /dev/null; then
        local vmalert_ready=$(kubectl get deployment vmalert-monitoring -n monitoring -o jsonpath='{.status.readyReplicas}')
        local vmalert_desired=$(kubectl get deployment vmalert-monitoring -n monitoring -o jsonpath='{.spec.replicas}')
        if [ "$vmalert_ready" = "$vmalert_desired" ]; then
            log_success "VMAlert运行正常 ($vmalert_ready/$vmalert_desired)"
        else
            log_error "VMAlert未就绪 ($vmalert_ready/$vmalert_desired)"
            return 1
        fi
    else
        log_error "VMAlert未部署"
        return 1
    fi
    
    # 检查AlertManager
    if kubectl get deployment alertmanager -n monitoring &> /dev/null; then
        local am_ready=$(kubectl get deployment alertmanager -n monitoring -o jsonpath='{.status.readyReplicas}')
        local am_desired=$(kubectl get deployment alertmanager -n monitoring -o jsonpath='{.spec.replicas}')
        if [ "$am_ready" = "$am_desired" ]; then
            log_success "AlertManager运行正常 ($am_ready/$am_desired)"
        else
            log_error "AlertManager未就绪 ($am_ready/$am_desired)"
            return 1
        fi
    else
        log_error "AlertManager未部署"
        return 1
    fi
    
    return 0
}

# 检查告警规则
check_alert_rules() {
    log_info "检查告警规则..."
    
    # 检查VMRule
    local rules_count=$(kubectl get vmrule -n monitoring --no-headers 2>/dev/null | wc -l)
    if [ "$rules_count" -gt 0 ]; then
        log_success "发现 $rules_count 个告警规则"
        kubectl get vmrule -n monitoring
    else
        log_error "未发现告警规则"
        return 1
    fi
    
    return 0
}

# 创建测试告警
create_test_alert() {
    log_info "创建测试告警..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-alert-rules
  namespace: monitoring
  labels:
    app: alert-rules
data:
  test-rules.yml: |
    groups:
      - name: test-alerts
        rules:
          - alert: TestAlert1Level
            expr: vector(1)
            for: 1m
            labels:
              severity: critical
              level: "1"
              test: "true"
            annotations:
              summary: "测试1级告警"
              description: "这是一个测试1级告警，用于验证告警系统功能"
              
          - alert: TestAlert2Level
            expr: vector(1)
            for: 1m
            labels:
              severity: warning
              level: "2"
              test: "true"
            annotations:
              summary: "测试2级告警"
              description: "这是一个测试2级告警，用于验证告警系统功能"
              
          - alert: TestAlert3Level
            expr: vector(1)
            for: 1m
            labels:
              severity: info
              level: "3"
              test: "true"
            annotations:
              summary: "测试3级告警"
              description: "这是一个测试3级告警，用于验证告警系统功能"
EOF
    
    log_success "测试告警规则已创建"
}

# 等待告警触发
wait_for_alerts() {
    log_info "等待告警触发（需要约2-3分钟）..."
    
    local max_wait=180  # 3分钟
    local wait_time=0
    local check_interval=10
    
    while [ $wait_time -lt $max_wait ]; do
        # 检查是否有活跃告警
        local alerts_count=$(kubectl exec -n monitoring deployment/alertmanager -- \
            wget -qO- http://localhost:9093/api/v1/alerts | \
            grep -o '"state":"firing"' | wc -l 2>/dev/null || echo "0")
        
        if [ "$alerts_count" -gt 0 ]; then
            log_success "检测到 $alerts_count 个活跃告警"
            return 0
        fi
        
        echo -n "."
        sleep $check_interval
        wait_time=$((wait_time + check_interval))
    done
    
    echo ""
    log_warning "等待超时，未检测到告警触发"
    return 1
}

# 检查告警状态
check_alert_status() {
    log_info "检查告警状态..."
    
    # 通过AlertManager API检查告警
    kubectl exec -n monitoring deployment/alertmanager -- \
        wget -qO- http://localhost:9093/api/v1/alerts | \
        python3 -m json.tool 2>/dev/null || \
        log_warning "无法解析AlertManager API响应"
    
    # 检查VMAlert状态
    log_info "VMAlert告警状态:"
    kubectl exec -n monitoring deployment/vmalert-monitoring -- \
        wget -qO- http://localhost:8080/api/v1/alerts | \
        python3 -m json.tool 2>/dev/null || \
        log_warning "无法解析VMAlert API响应"
}

# 测试通知功能
test_notifications() {
    log_info "测试通知功能..."
    
    # 发送测试通知到AlertManager
    cat <<EOF > /tmp/test-alert.json
[
  {
    "labels": {
      "alertname": "TestNotification",
      "severity": "warning",
      "level": "2",
      "instance": "test-instance",
      "service": "test-service"
    },
    "annotations": {
      "summary": "测试通知",
      "description": "这是一个测试通知，用于验证通知系统功能"
    },
    "startsAt": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
    "generatorURL": "http://test-generator"
  }
]
EOF
    
    # 发送到AlertManager
    kubectl exec -n monitoring deployment/alertmanager -- \
        wget --post-file=/dev/stdin --header="Content-Type: application/json" \
        -qO- http://localhost:9093/api/v1/alerts < /tmp/test-alert.json
    
    log_success "测试通知已发送"
    rm -f /tmp/test-alert.json
}

# 清理测试告警
cleanup_test_alerts() {
    log_info "清理测试告警..."
    
    # 删除测试告警规则
    kubectl delete configmap test-alert-rules -n monitoring --ignore-not-found=true
    
    # 等待告警清除
    sleep 30
    
    log_success "测试告警已清理"
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    local report_file="alert-test-report.txt"
    
    cat > "$report_file" << EOF
告警系统测试报告
生成时间: $(date)
=====================================

服务状态检查:
EOF
    
    # 检查各个服务状态
    kubectl get pods -n monitoring >> "$report_file" 2>&1
    
    cat >> "$report_file" << EOF

告警规则检查:
EOF
    
    kubectl get vmrule -n monitoring >> "$report_file" 2>&1
    
    cat >> "$report_file" << EOF

当前活跃告警:
EOF
    
    kubectl exec -n monitoring deployment/alertmanager -- \
        wget -qO- http://localhost:9093/api/v1/alerts 2>/dev/null >> "$report_file" || \
        echo "无法获取告警信息" >> "$report_file"
    
    cat >> "$report_file" << EOF

测试结果:
- 服务状态检查: $(check_services &>/dev/null && echo "通过" || echo "失败")
- 告警规则检查: $(check_alert_rules &>/dev/null && echo "通过" || echo "失败")
- 告警触发测试: 需要手动验证
- 通知功能测试: 需要手动验证

建议:
1. 检查Webhook URL配置是否正确
2. 验证SMTP服务器配置
3. 测试各级告警的通知渠道
4. 定期执行告警测试
EOF
    
    log_success "测试报告已生成: $report_file"
}

# 显示使用帮助
show_help() {
    echo "告警测试脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --check         仅检查服务状态"
    echo "  -t, --test          执行完整测试"
    echo "  -n, --notify        测试通知功能"
    echo "  -r, --report        生成测试报告"
    echo "  --cleanup           清理测试告警"
    echo ""
    echo "示例:"
    echo "  $0 --check         # 检查服务状态"
    echo "  $0 --test          # 执行完整测试"
    echo "  $0 --notify        # 测试通知功能"
    echo ""
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--check)
            check_services
            check_alert_rules
            ;;
        -t|--test)
            log_info "开始执行完整告警测试..."
            check_services
            check_alert_rules
            create_test_alert
            wait_for_alerts
            check_alert_status
            cleanup_test_alerts
            generate_test_report
            log_success "告警测试完成"
            ;;
        -n|--notify)
            test_notifications
            ;;
        -r|--report)
            generate_test_report
            ;;
        --cleanup)
            cleanup_test_alerts
            ;;
        "")
            log_info "开始基础检查..."
            check_services
            check_alert_rules
            log_info "基础检查完成，使用 --help 查看更多选项"
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
