apiVersion: v1
kind: ConfigMap
metadata:
  name: alert-rules
  namespace: monitoring
data:
  alert-rules.yml: |
    groups:
      # 1级告警规则 - 严重告警
      - name: critical-alerts
        rules:
          # 服务完全不可用
          - alert: ServiceDown
            expr: up == 0
            for: 1m
            labels:
              severity: critical
              level: "1"
            annotations:
              summary: "服务 {{ $labels.instance }} 完全不可用"
              description: "服务 {{ $labels.instance }} 已经宕机超过1分钟，需要立即处理"
              
          # API服务器不可用
          - alert: KubernetesAPIServerDown
            expr: up{job="kubernetes-apiservers"} == 0
            for: 1m
            labels:
              severity: critical
              level: "1"
            annotations:
              summary: "Kubernetes API服务器不可用"
              description: "Kubernetes API服务器 {{ $labels.instance }} 不可用，集群管理功能受影响"
              
          # ETCD集群不可用
          - alert: EtcdClusterDown
            expr: up{job="OPSClusterEtcd"} == 0
            for: 1m
            labels:
              severity: critical
              level: "1"
            annotations:
              summary: "ETCD集群节点不可用"
              description: "ETCD节点 {{ $labels.instance }} 不可用，可能影响集群数据一致性"
              
          # 节点不可用
          - alert: NodeDown
            expr: up{job="kubernetes-nodes"} == 0
            for: 2m
            labels:
              severity: critical
              level: "1"
            annotations:
              summary: "Kubernetes节点不可用"
              description: "节点 {{ $labels.instance }} 不可用超过2分钟"
              
          # 内存使用率过高
          - alert: HighMemoryUsage
            expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
            for: 5m
            labels:
              severity: critical
              level: "1"
            annotations:
              summary: "内存使用率过高"
              description: "节点 {{ $labels.instance }} 内存使用率超过90%，当前值: {{ $value }}%"
              
          # 磁盘空间不足
          - alert: DiskSpaceLow
            expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
            for: 5m
            labels:
              severity: critical
              level: "1"
            annotations:
              summary: "磁盘空间不足"
              description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过90%，当前值: {{ $value }}%"

      # 2级告警规则 - 警告告警
      - name: warning-alerts
        rules:
          # CPU使用率高
          - alert: HighCPUUsage
            expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
            for: 10m
            labels:
              severity: warning
              level: "2"
            annotations:
              summary: "CPU使用率过高"
              description: "节点 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"
              
          # 内存使用率警告
          - alert: MemoryUsageWarning
            expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 75
            for: 10m
            labels:
              severity: warning
              level: "2"
            annotations:
              summary: "内存使用率警告"
              description: "节点 {{ $labels.instance }} 内存使用率超过75%，当前值: {{ $value }}%"
              
          # 磁盘空间警告
          - alert: DiskSpaceWarning
            expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 80
            for: 10m
            labels:
              severity: warning
              level: "2"
            annotations:
              summary: "磁盘空间警告"
              description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过80%，当前值: {{ $value }}%"
              
          # 网络连接数过高
          - alert: HighNetworkConnections
            expr: node_netstat_Tcp_CurrEstab > 10000
            for: 10m
            labels:
              severity: warning
              level: "2"
            annotations:
              summary: "网络连接数过高"
              description: "节点 {{ $labels.instance }} TCP连接数超过10000，当前值: {{ $value }}"
              
          # Nginx错误率高
          - alert: NginxHighErrorRate
            expr: rate(nginx_http_requests_total{status=~"5.."}[5m]) / rate(nginx_http_requests_total[5m]) * 100 > 5
            for: 5m
            labels:
              severity: warning
              level: "2"
            annotations:
              summary: "Nginx错误率过高"
              description: "Nginx 5xx错误率超过5%，当前值: {{ $value }}%"
              
          # Pod重启频繁
          - alert: PodRestartingFrequently
            expr: increase(kube_pod_container_status_restarts_total[1h]) > 5
            for: 5m
            labels:
              severity: warning
              level: "2"
            annotations:
              summary: "Pod重启频繁"
              description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} 在1小时内重启超过5次"

      # 3级告警规则 - 信息告警
      - name: info-alerts
        rules:
          # CPU使用率提醒
          - alert: CPUUsageInfo
            expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 60
            for: 15m
            labels:
              severity: info
              level: "3"
            annotations:
              summary: "CPU使用率提醒"
              description: "节点 {{ $labels.instance }} CPU使用率超过60%，当前值: {{ $value }}%"
              
          # 内存使用率提醒
          - alert: MemoryUsageInfo
            expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 60
            for: 15m
            labels:
              severity: info
              level: "3"
            annotations:
              summary: "内存使用率提醒"
              description: "节点 {{ $labels.instance }} 内存使用率超过60%，当前值: {{ $value }}%"
              
          # 磁盘空间提醒
          - alert: DiskSpaceInfo
            expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 70
            for: 15m
            labels:
              severity: info
              level: "3"
            annotations:
              summary: "磁盘空间提醒"
              description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过70%，当前值: {{ $value }}%"
              
          # 负载均衡器响应时间
          - alert: LoadBalancerSlowResponse
            expr: histogram_quantile(0.95, rate(nginx_http_request_duration_seconds_bucket[5m])) > 2
            for: 10m
            labels:
              severity: info
              level: "3"
            annotations:
              summary: "负载均衡器响应时间较慢"
              description: "Nginx 95%分位响应时间超过2秒，当前值: {{ $value }}秒"
              
          # 证书即将过期
          - alert: SSLCertificateExpiringSoon
            expr: (ssl_certificate_expiry_seconds - time()) / 86400 < 30
            for: 1h
            labels:
              severity: info
              level: "3"
            annotations:
              summary: "SSL证书即将过期"
              description: "域名 {{ $labels.instance }} 的SSL证书将在 {{ $value }} 天后过期"
