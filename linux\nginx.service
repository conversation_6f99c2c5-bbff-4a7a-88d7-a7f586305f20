[Unit]
Description=nginx - high performance web server
Documentation=https://nginx.org/en/docs/
After=network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Service]
Type=forking
PIDFile=/run/nginx.pid
ExecStartPre=/usr/sbin/nginx -t -q -g 'daemon on; master_process on;'
ExecStart=/usr/sbin/nginx -g 'daemon on; master_process on;'
ExecReload=/usr/sbin/nginx -g 'daemon on; master_process on;' -s reload
ExecStop=-/sbin/start-stop-daemon --quiet --stop --retry QUIT/5 --pidfile /run/nginx.pid
TimeoutStopSec=5
KillMode=mixed

# 资源限制 
LimitNOFILE=262144                    # 文件描述符限制调小
LimitNPROC=262144                     # 进程数限制调小
LimitCORE=infinity                    # 保持不变
LimitMEMLOCK=infinity                # 保持不变

# CPU 优化 - 16核配置
CPUAccounting=yes
CPUQuota=1200%                        # 限制在12个核心,预留资源给系统
CPUSchedulingPolicy=batch            # 使用 batch 策略更适合
CPUSchedulingPriority=0              # 使用默认优先级

# 内存优化 - 32G配置
MemoryAccounting=yes
MemoryHigh=24G                       # 降低警告阈值
MemoryMax=28G                        # 预留4G给系统
OOMScoreAdjust=-500                 # 调高些,让其他重要进程有机会

# IO 优化
IOAccounting=yes
IOSchedulingClass=best-effort        # 使用 best-effort 更合适
IOSchedulingPriority=4               # 中等优先级

# 安全限制
PrivateDevices=yes                   # 增加安全限制
PrivateTmp=yes
ProtectSystem=full
ProtectHome=yes
ReadOnlyDirectories=/etc/nginx
ReadWriteDirectories=/var/log/nginx /var/cache/nginx /run

# 重启策略
Restart=always
RestartSec=5s

[Install]
WantedBy=multi-user.target
