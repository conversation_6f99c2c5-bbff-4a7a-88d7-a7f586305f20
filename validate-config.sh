#!/bin/bash

# 监控告警配置验证脚本
# 作者: 运维团队
# 版本: 1.0
# 描述: 验证监控告警配置的正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证YAML文件语法
validate_yaml_syntax() {
    local file=$1
    log_info "验证 $file 语法..."
    
    if [ ! -f "$file" ]; then
        log_error "文件 $file 不存在"
        return 1
    fi
    
    # 使用kubectl dry-run验证语法
    if kubectl apply --dry-run=client -f "$file" &> /dev/null; then
        log_success "$file 语法正确"
        return 0
    else
        log_error "$file 语法错误"
        kubectl apply --dry-run=client -f "$file"
        return 1
    fi
}

# 验证告警规则
validate_alert_rules() {
    log_info "验证告警规则..."
    
    local rules_file="alert-rules.yaml"
    if [ ! -f "$rules_file" ]; then
        log_error "告警规则文件不存在: $rules_file"
        return 1
    fi
    
    # 检查必要的告警级别
    local required_levels=("critical" "warning" "info")
    for level in "${required_levels[@]}"; do
        if grep -q "severity: $level" "$rules_file"; then
            log_success "发现 $level 级别告警规则"
        else
            log_warning "未发现 $level 级别告警规则"
        fi
    done
    
    # 检查告警规则格式
    if grep -q "groups:" "$rules_file" && grep -q "rules:" "$rules_file"; then
        log_success "告警规则格式正确"
    else
        log_error "告警规则格式错误，缺少groups或rules字段"
        return 1
    fi
    
    return 0
}

# 验证AlertManager配置
validate_alertmanager_config() {
    log_info "验证AlertManager配置..."
    
    local config_file="alertmanager-config.yaml"
    if [ ! -f "$config_file" ]; then
        log_error "AlertManager配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查路由配置
    if grep -q "route:" "$config_file"; then
        log_success "发现路由配置"
    else
        log_error "缺少路由配置"
        return 1
    fi
    
    # 检查接收器配置
    if grep -q "receivers:" "$config_file"; then
        log_success "发现接收器配置"
    else
        log_error "缺少接收器配置"
        return 1
    fi
    
    # 检查告警级别路由
    local required_receivers=("level1-alerts" "level2-alerts" "level3-alerts")
    for receiver in "${required_receivers[@]}"; do
        if grep -q "$receiver" "$config_file"; then
            log_success "发现 $receiver 接收器"
        else
            log_warning "未发现 $receiver 接收器"
        fi
    done
    
    return 0
}

# 验证VMAgent配置
validate_vmagent_config() {
    log_info "验证VMAgent配置..."
    
    local config_file="vmagent-cr.yaml"
    if [ ! -f "$config_file" ]; then
        log_error "VMAgent配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查抓取配置
    if grep -q "scrapeConfigs:" "$config_file"; then
        log_success "发现抓取配置"
    else
        log_error "缺少抓取配置"
        return 1
    fi
    
    # 检查远程写入配置
    if grep -q "remoteWrite:" "$config_file"; then
        log_success "发现远程写入配置"
    else
        log_warning "未发现远程写入配置"
    fi
    
    # 检查重要的监控目标
    local required_jobs=("kubernetes-apiservers" "kubernetes-nodes" "OPSClusterCoreDNS" "OPSClusterEtcd")
    for job in "${required_jobs[@]}"; do
        if grep -q "$job" "$config_file"; then
            log_success "发现监控任务: $job"
        else
            log_warning "未发现监控任务: $job"
        fi
    done
    
    return 0
}

# 验证通知模板
validate_notification_templates() {
    log_info "验证通知模板..."
    
    local template_file="notification-templates.yaml"
    if [ ! -f "$template_file" ]; then
        log_error "通知模板文件不存在: $template_file"
        return 1
    fi
    
    # 检查模板类型
    local template_types=("slack" "email" "wechat" "dingtalk")
    for template in "${template_types[@]}"; do
        if grep -q "$template" "$template_file"; then
            log_success "发现 $template 模板"
        else
            log_warning "未发现 $template 模板"
        fi
    done
    
    return 0
}

# 验证网络连接
validate_network_connectivity() {
    log_info "验证网络连接..."
    
    # 检查kubectl连接
    if kubectl cluster-info &> /dev/null; then
        log_success "Kubernetes集群连接正常"
    else
        log_error "无法连接到Kubernetes集群"
        return 1
    fi
    
    # 检查命名空间
    if kubectl get namespace monitoring &> /dev/null; then
        log_success "monitoring命名空间存在"
    else
        log_warning "monitoring命名空间不存在，部署时会自动创建"
    fi
    
    return 0
}

# 验证资源配额
validate_resource_requirements() {
    log_info "验证资源需求..."
    
    # 检查节点资源
    local total_cpu=$(kubectl top nodes --no-headers 2>/dev/null | awk '{sum += $3} END {print sum}' || echo "0")
    local total_memory=$(kubectl top nodes --no-headers 2>/dev/null | awk '{sum += $5} END {print sum}' || echo "0")
    
    if [ "$total_cpu" != "0" ] && [ "$total_memory" != "0" ]; then
        log_success "节点资源信息获取成功"
        log_info "当前CPU使用: ${total_cpu}m"
        log_info "当前内存使用: ${total_memory}Mi"
    else
        log_warning "无法获取节点资源信息，请确保metrics-server已安装"
    fi
    
    # 检查存储类
    if kubectl get storageclass &> /dev/null; then
        local default_sc=$(kubectl get storageclass -o jsonpath='{.items[?(@.metadata.annotations.storageclass\.kubernetes\.io/is-default-class=="true")].metadata.name}')
        if [ -n "$default_sc" ]; then
            log_success "发现默认存储类: $default_sc"
        else
            log_warning "未发现默认存储类，可能需要手动指定"
        fi
    else
        log_warning "无法获取存储类信息"
    fi
    
    return 0
}

# 生成配置报告
generate_config_report() {
    log_info "生成配置报告..."
    
    local report_file="config-validation-report.txt"
    
    cat > "$report_file" << EOF
监控告警配置验证报告
生成时间: $(date)
=====================================

配置文件检查:
EOF
    
    # 检查各个配置文件
    local config_files=("vmagent-cr.yaml" "alertmanager-config.yaml" "alert-rules.yaml" "vmalert-config.yaml" "notification-templates.yaml")
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            echo "✓ $file - 存在" >> "$report_file"
        else
            echo "✗ $file - 不存在" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

告警级别配置:
- 1级告警: 电话 + 小闪 + 小闪群
- 2级告警: 小闪 + 小闪群  
- 3级告警: 小闪群

监控目标:
- Kubernetes API Server
- Kubernetes Nodes
- CoreDNS
- ETCD
- Nginx
- Node Exporter

通知方式:
- Slack
- 邮件
- 微信
- 钉钉

建议:
1. 请根据实际环境修改Webhook URL
2. 配置正确的SMTP服务器信息
3. 调整资源限制以适应集群规模
4. 定期测试告警通知功能
EOF
    
    log_success "配置报告已生成: $report_file"
}

# 主验证函数
main() {
    log_info "开始验证监控告警配置..."
    
    local validation_passed=true
    
    # 执行各项验证
    validate_network_connectivity || validation_passed=false
    validate_yaml_syntax "vmagent-cr.yaml" || validation_passed=false
    validate_yaml_syntax "alertmanager-config.yaml" || validation_passed=false
    validate_yaml_syntax "alert-rules.yaml" || validation_passed=false
    validate_yaml_syntax "vmalert-config.yaml" || validation_passed=false
    validate_yaml_syntax "notification-templates.yaml" || validation_passed=false
    
    validate_vmagent_config || validation_passed=false
    validate_alertmanager_config || validation_passed=false
    validate_alert_rules || validation_passed=false
    validate_notification_templates || validation_passed=false
    validate_resource_requirements || validation_passed=false
    
    generate_config_report
    
    if [ "$validation_passed" = true ]; then
        log_success "所有验证通过！可以开始部署"
        echo ""
        echo "下一步:"
        echo "1. 修改配置文件中的Webhook URL和SMTP信息"
        echo "2. 运行部署脚本: ./deploy-monitoring.sh"
        echo "3. 验证部署结果"
        return 0
    else
        log_error "验证失败，请修复错误后重新验证"
        return 1
    fi
}

# 执行主函数
main "$@"
