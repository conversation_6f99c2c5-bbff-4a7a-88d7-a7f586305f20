# 错误页面
error_page 400 401 402 403 404 405 406 407 408 409 410 411 412 413 414 415 416 417 418 421 422 423 424 426 428 429 431 451 /4xx.html;
error_page 500 501 502 503 504 505 506 507 508 510 511 /5xx.html;

location = /4xx.html {
    root /usr/share/nginx/html;
    internal;
}

location = /5xx.html {
    root /usr/share/nginx/html;
    internal;
}

# 健康检查
location = /health {
    access_log off;
    add_header Content-Type text/plain;
    return 200 "OK\n";
}

# 监控指标
location = /metrics {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow 192.168.0.0/16;
    allow 172.10.0.0/16;
    deny all;
}

# 禁止访问隐藏文件
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}