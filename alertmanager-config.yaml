apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      # 全局配置
      smtp_smarthost: 'smtp.example.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'your-password'
      
    # 路由配置 - 根据告警级别分发
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default-receiver'
      routes:
        # 1级告警 - 严重告警
        - match:
            severity: critical
          receiver: 'level1-alerts'
          group_wait: 5s
          repeat_interval: 30m
          
        # 2级告警 - 警告告警  
        - match:
            severity: warning
          receiver: 'level2-alerts'
          group_wait: 10s
          repeat_interval: 1h
          
        # 3级告警 - 信息告警
        - match:
            severity: info
          receiver: 'level3-alerts'
          group_wait: 30s
          repeat_interval: 4h

    # 接收器配置
    receivers:
      # 默认接收器
      - name: 'default-receiver'
        webhook_configs:
          - url: 'http://webhook-service:8080/alerts'
            
      # 1级告警接收器 - 电话 + 小闪 + 小闪群
      - name: 'level1-alerts'
        # 电话告警 (通过webhook调用电话接口)
        webhook_configs:
          - url: 'http://phone-alert-service:8080/call'
            title: '【严重告警】{{ .GroupLabels.alertname }}'
            send_resolved: true
            http_config:
              basic_auth:
                username: 'alert-user'
                password: 'alert-password'
        # 小闪个人通知
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
            channel: '@oncall-engineer'
            title: '🚨 1级严重告警'
            text: |
              告警名称: {{ .GroupLabels.alertname }}
              告警级别: {{ .CommonLabels.severity }}
              集群: {{ .CommonLabels.cluster }}
              服务: {{ .CommonLabels.service }}
              详情: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
            send_resolved: true
        # 小闪群组通知
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/TEAM/WEBHOOK'
            channel: '#ops-alerts'
            title: '🚨 1级严重告警'
            text: |
              @channel 严重告警需要立即处理！
              告警名称: {{ .GroupLabels.alertname }}
              告警级别: {{ .CommonLabels.severity }}
              集群: {{ .CommonLabels.cluster }}
              服务: {{ .CommonLabels.service }}
              详情: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
            send_resolved: true
            
      # 2级告警接收器 - 小闪 + 小闪群
      - name: 'level2-alerts'
        # 小闪个人通知
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
            channel: '@oncall-engineer'
            title: '⚠️ 2级警告告警'
            text: |
              告警名称: {{ .GroupLabels.alertname }}
              告警级别: {{ .CommonLabels.severity }}
              集群: {{ .CommonLabels.cluster }}
              服务: {{ .CommonLabels.service }}
              详情: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
            send_resolved: true
        # 小闪群组通知
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/TEAM/WEBHOOK'
            channel: '#ops-alerts'
            title: '⚠️ 2级警告告警'
            text: |
              告警名称: {{ .GroupLabels.alertname }}
              告警级别: {{ .CommonLabels.severity }}
              集群: {{ .CommonLabels.cluster }}
              服务: {{ .CommonLabels.service }}
              详情: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
            send_resolved: true
            
      # 3级告警接收器 - 小闪群
      - name: 'level3-alerts'
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/TEAM/WEBHOOK'
            channel: '#ops-alerts'
            title: 'ℹ️ 3级信息告警'
            text: |
              告警名称: {{ .GroupLabels.alertname }}
              告警级别: {{ .CommonLabels.severity }}
              集群: {{ .CommonLabels.cluster }}
              服务: {{ .CommonLabels.service }}
              详情: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
            send_resolved: true

    # 抑制规则 - 避免重复告警
    inhibit_rules:
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'cluster', 'service']
      - source_match:
          severity: 'warning'
        target_match:
          severity: 'info'
        equal: ['alertname', 'cluster', 'service']

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.25.0
        ports:
        - containerPort: 9093
        volumeMounts:
        - name: config
          mountPath: /etc/alertmanager
        - name: storage
          mountPath: /alertmanager
        args:
          - '--config.file=/etc/alertmanager/alertmanager.yml'
          - '--storage.path=/alertmanager'
          - '--web.external-url=http://alertmanager.example.com'
          - '--cluster.listen-address=0.0.0.0:9094'
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: config
        configMap:
          name: alertmanager-config
      - name: storage
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: monitoring
spec:
  selector:
    app: alertmanager
  ports:
  - port: 9093
    targetPort: 9093
  type: ClusterIP
