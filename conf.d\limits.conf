# IP限流
limit_req_zone $binary_remote_addr zone=req_zone:64m rate=100r/s;
limit_conn_zone $binary_remote_addr zone=conn_zone:64m;

# 服务器限流
limit_conn_zone $server_name zone=server_zone:64m;
limit_req_zone $server_name zone=server_req_zone:64m rate=1000r/s;

# 定义 API Key 变量
map $http_x_api_key $api_key {
    default $http_x_api_key;
    ""      "anonymous";
}

# 自定义变量限流
limit_req_zone $api_key zone=api_zone:64m rate=10r/s;

# 限流配置
limit_req zone=req_zone burst=20 nodelay;
limit_conn conn_zone 100;
limit_conn server_zone 1000;
limit_rate 1024k;

# 限流白名单
geo $limit_ip {
    default 1;
    127.0.0.1 0;
    10.0.0.0/8 0;
    **********/12 0;
    ***********/16 0;
}