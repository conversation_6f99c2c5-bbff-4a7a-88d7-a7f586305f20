# 网络连接优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 65535
net.core.rmem_default = 16777216            # 提高默认接收缓冲区
net.core.wmem_default = 16777216            # 提高默认发送缓冲区
net.core.rmem_max = 33554432                # 提高最大接收缓冲区
net.core.wmem_max = 33554432                # 提高最大发送缓冲区
net.core.optmem_max = 8388608

# TCP 优化
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_max_tw_buckets = 1048576
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
net.ipv4.tcp_max_orphans = 262144
net.ipv4.tcp_syncookies = 1

# TCP 缓冲区
net.ipv4.tcp_rmem = 4096 87380 33554432    # 调整 TCP 接收缓冲区
net.ipv4.tcp_wmem = 4096 87380 33554432    # 调整 TCP 发送缓冲区
net.ipv4.tcp_mem = 786432 1048576 1572864

# TCP BBR 拥塞控制
net.core.default_qdisc = fq
net.ipv4.tcp_congestion_control = bbr

# 内存管理优化
vm.swappiness = 0                           # 降低使用 swap 的倾向
vm.dirty_ratio = 40                         # 降低内存脏页比例
vm.dirty_background_ratio = 10              # 降低后台刷新阈值
vm.min_free_kbytes = 1048576               # 提高最小空闲内存
vm.max_map_count = 262144
vm.overcommit_memory = 1
vm.overcommit_ratio = 90

# 文件系统
fs.file-max = 2097152
fs.nr_open = 2097152
fs.inotify.max_user_watches = 524288

# 内核安全
kernel.core_uses_pid = 1
kernel.core_pattern = /var/crash/core-%e-%s-%u-%g-%p-%t
kernel.sysrq = 0
kernel.msgmnb = 65536
kernel.msgmax = 65536
kernel.shmmax = 68719476736
kernel.shmall = 4294967296
kernel.panic = 10
kernel.pid_max = 65536

# 网络安全
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
