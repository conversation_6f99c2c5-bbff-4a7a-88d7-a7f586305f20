- job_name: 'OPSClusterCoreDNS'
  kubernetes_sd_configs:
    - role: pod
  relabel_configs:
    # 选择 CoreDNS pods
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_k8s_app]
      action: keep
      regex: kube-system;kube-dns
    # 或者使用这个标签选择器（取决于您的集群如何标记 CoreDNS）
    # - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_k8s_app]
    #   action: keep
    #   regex: kube-system;coredns
    # 设置指标路径
    - action: replace
      target_label: __metrics_path__
      replacement: /metrics
    # 添加端口
    - action: replace
      target_label: __address__
      replacement: ${1}:9153
      source_labels: [__meta_kubernetes_pod_ip]
    # 保留有用的标签
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)
    # 设置实例名称
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: instance
    # 添加命名空间标签
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace