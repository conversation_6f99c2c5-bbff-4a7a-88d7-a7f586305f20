# 主配置文件
user nginx;

# CPU 相关优化
worker_processes auto;                    
worker_cpu_affinity auto;                 
worker_priority -10;                      

# 文件句柄和核心转储
worker_rlimit_nofile 1048576;                     
worker_shutdown_timeout 30s;              

# 性能优化
timer_resolution 50ms;                    
thread_pool default threads=64;           
# aio threads=default;                    # 这行有问题，需要移动到 http 块内

# 错误日志
error_log /usr/local/openresty/nginx/logs/error.log notice;
pid /usr/local/openresty/nginx/logs/nginx.pid;

# 工作进程配置
events {
    use epoll;                            
    worker_connections 65535;             
    multi_accept on;                      
    accept_mutex off;                     
    accept_mutex_delay 50ms;              
}

# HTTP核心配置
http {
    include /usr/local/openresty/nginx/conf/mime.types;
    default_type application/octet-stream;
    charset utf-8;
    
    # 这里是 aio 指令的正确位置
    aio threads=default;
    
    # 包含 GeoIP 配置
    include /usr/local/openresty/nginx/conf/conf.d/geoip.conf;
    
    # 包含优化配置
    include /usr/local/openresty/nginx/conf/conf.d/optimization.conf;
    
    # 包含SSL配置
    include /usr/local/openresty/nginx/conf/conf.d/ssl.conf;
    
    # 包含安全配置
    include /usr/local/openresty/nginx/conf/conf.d/security.conf;
    
    # 包含日志配置
    include /usr/local/openresty/nginx/conf/conf.d/logging.conf;
    
    # 包含限流配置
    include /usr/local/openresty/nginx/conf/conf.d/limits.conf;
    
    # 包含代理配置
    include /usr/local/openresty/nginx/conf/conf.d/proxy.conf;
    
    # 包含缓存配置
    include /usr/local/openresty/nginx/conf/conf.d/cache.conf;

    # 包含上游配置
    include /usr/local/openresty/nginx/conf/upstream.d/*.conf;
    
    # 包含虚拟主机配置
    include /usr/local/openresty/nginx/conf/vhost.d/*.conf;
}
