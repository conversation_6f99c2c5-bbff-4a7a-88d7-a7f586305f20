# 静态文件处理
location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml)$ {
    access_log off;
    log_not_found off;
    expires max;
    add_header Cache-Control "public, no-transform, immutable";
    add_header X-Content-Type-Options nosniff;
    tcp_nodelay off;
    open_file_cache max=3000 inactive=120s;
    open_file_cache_valid 45s;
    open_file_cache_min_uses 2;
    open_file_cache_errors off;
}

# 主要业务逻辑处理
location / {
    try_files $uri $uri/ /index.html;
}
