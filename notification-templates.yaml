apiVersion: v1
kind: ConfigMap
metadata:
  name: notification-templates
  namespace: monitoring
data:
  # Slack通知模板
  slack-templates.tmpl: |
    {{ define "slack.title" }}
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.level "1" }}🚨 1级严重告警{{ end }}
      {{ if eq .CommonLabels.level "2" }}⚠️ 2级警告告警{{ end }}
      {{ if eq .CommonLabels.level "3" }}ℹ️ 3级信息告警{{ end }}
    {{ else }}
      ✅ 告警已恢复
    {{ end }}
    {{ end }}

    {{ define "slack.text" }}
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.level "1" }}@channel 严重告警需要立即处理！{{ end }}
    {{ end }}
    
    *告警详情:*
    • 告警名称: `{{ .GroupLabels.alertname }}`
    • 告警级别: `{{ .CommonLabels.severity }}` ({{ .CommonLabels.level }}级)
    • 集群: `{{ .CommonLabels.cluster | default "未知" }}`
    • 服务: `{{ .CommonLabels.service | default "未知" }}`
    • 状态: `{{ .Status }}`
    
    {{ if eq .Status "firing" }}
    *触发的告警:*
    {{ range .Alerts }}
    • {{ .Annotations.summary }}
      - 详情: {{ .Annotations.description }}
      - 实例: {{ .Labels.instance }}
      - 开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
    {{ end }}
    {{ else }}
    *已恢复的告警:*
    {{ range .Alerts }}
    • {{ .Annotations.summary }}
      - 实例: {{ .Labels.instance }}
      - 恢复时间: {{ .EndsAt.Format "2006-01-02 15:04:05" }}
    {{ end }}
    {{ end }}
    {{ end }}

  # 邮件通知模板
  email-templates.tmpl: |
    {{ define "email.subject" }}
    {{ if eq .Status "firing" }}
      [告警] {{ .CommonLabels.level }}级 - {{ .GroupLabels.alertname }}
    {{ else }}
      [恢复] {{ .GroupLabels.alertname }}
    {{ end }}
    {{ end }}

    {{ define "email.html" }}
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background-color: {{ if eq .Status "firing" }}{{ if eq .CommonLabels.level "1" }}#dc3545{{ else if eq .CommonLabels.level "2" }}#fd7e14{{ else }}#17a2b8{{ end }}{{ else }}#28a745{{ end }}; color: white; padding: 15px; border-radius: 5px; }
            .content { margin: 20px 0; }
            .alert-item { background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid {{ if eq .Status "firing" }}{{ if eq .CommonLabels.level "1" }}#dc3545{{ else if eq .CommonLabels.level "2" }}#fd7e14{{ else }}#17a2b8{{ end }}{{ else }}#28a745{{ end }}; }
            .label { font-weight: bold; }
            .timestamp { color: #6c757d; font-size: 0.9em; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>
                {{ if eq .Status "firing" }}
                  {{ if eq .CommonLabels.level "1" }}🚨 1级严重告警{{ end }}
                  {{ if eq .CommonLabels.level "2" }}⚠️ 2级警告告警{{ end }}
                  {{ if eq .CommonLabels.level "3" }}ℹ️ 3级信息告警{{ end }}
                {{ else }}
                  ✅ 告警已恢复
                {{ end }}
            </h2>
        </div>
        
        <div class="content">
            <p><span class="label">告警名称:</span> {{ .GroupLabels.alertname }}</p>
            <p><span class="label">告警级别:</span> {{ .CommonLabels.severity }} ({{ .CommonLabels.level }}级)</p>
            <p><span class="label">集群:</span> {{ .CommonLabels.cluster | default "未知" }}</p>
            <p><span class="label">服务:</span> {{ .CommonLabels.service | default "未知" }}</p>
            <p><span class="label">状态:</span> {{ .Status }}</p>
            
            {{ if eq .Status "firing" }}
            <h3>触发的告警:</h3>
            {{ range .Alerts }}
            <div class="alert-item">
                <p><span class="label">摘要:</span> {{ .Annotations.summary }}</p>
                <p><span class="label">详情:</span> {{ .Annotations.description }}</p>
                <p><span class="label">实例:</span> {{ .Labels.instance }}</p>
                <p class="timestamp"><span class="label">开始时间:</span> {{ .StartsAt.Format "2006-01-02 15:04:05" }}</p>
            </div>
            {{ end }}
            {{ else }}
            <h3>已恢复的告警:</h3>
            {{ range .Alerts }}
            <div class="alert-item">
                <p><span class="label">摘要:</span> {{ .Annotations.summary }}</p>
                <p><span class="label">实例:</span> {{ .Labels.instance }}</p>
                <p class="timestamp"><span class="label">恢复时间:</span> {{ .EndsAt.Format "2006-01-02 15:04:05" }}</p>
            </div>
            {{ end }}
            {{ end }}
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #e9ecef; border-radius: 5px;">
            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                此邮件由监控系统自动发送，请勿回复。如有问题请联系运维团队。
            </p>
        </div>
    </body>
    </html>
    {{ end }}

  # 微信通知模板
  wechat-templates.tmpl: |
    {{ define "wechat.text" }}
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.level "1" }}🚨 1级严重告警{{ end }}
      {{ if eq .CommonLabels.level "2" }}⚠️ 2级警告告警{{ end }}
      {{ if eq .CommonLabels.level "3" }}ℹ️ 3级信息告警{{ end }}
    {{ else }}
      ✅ 告警已恢复
    {{ end }}
    
    告警名称: {{ .GroupLabels.alertname }}
    告警级别: {{ .CommonLabels.severity }} ({{ .CommonLabels.level }}级)
    集群: {{ .CommonLabels.cluster | default "未知" }}
    服务: {{ .CommonLabels.service | default "未知" }}
    状态: {{ .Status }}
    
    {{ if eq .Status "firing" }}
    触发的告警:
    {{ range .Alerts }}
    • {{ .Annotations.summary }}
      详情: {{ .Annotations.description }}
      实例: {{ .Labels.instance }}
      时间: {{ .StartsAt.Format "01-02 15:04:05" }}
    {{ end }}
    {{ else }}
    已恢复的告警:
    {{ range .Alerts }}
    • {{ .Annotations.summary }}
      实例: {{ .Labels.instance }}
      恢复时间: {{ .EndsAt.Format "01-02 15:04:05" }}
    {{ end }}
    {{ end }}
    {{ end }}

  # 钉钉通知模板
  dingtalk-templates.tmpl: |
    {{ define "dingtalk.title" }}
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.level "1" }}🚨 1级严重告警{{ end }}
      {{ if eq .CommonLabels.level "2" }}⚠️ 2级警告告警{{ end }}
      {{ if eq .CommonLabels.level "3" }}ℹ️ 3级信息告警{{ end }}
    {{ else }}
      ✅ 告警已恢复
    {{ end }}
    {{ end }}

    {{ define "dingtalk.text" }}
    ### {{ template "dingtalk.title" . }}
    
    {{ if eq .Status "firing" }}
      {{ if eq .CommonLabels.level "1" }}@所有人 严重告警需要立即处理！{{ end }}
    {{ end }}
    
    **告警详情:**
    - 告警名称: {{ .GroupLabels.alertname }}
    - 告警级别: {{ .CommonLabels.severity }} ({{ .CommonLabels.level }}级)
    - 集群: {{ .CommonLabels.cluster | default "未知" }}
    - 服务: {{ .CommonLabels.service | default "未知" }}
    - 状态: {{ .Status }}
    
    {{ if eq .Status "firing" }}
    **触发的告警:**
    {{ range .Alerts }}
    - {{ .Annotations.summary }}
      - 详情: {{ .Annotations.description }}
      - 实例: {{ .Labels.instance }}
      - 开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
    {{ end }}
    {{ else }}
    **已恢复的告警:**
    {{ range .Alerts }}
    - {{ .Annotations.summary }}
      - 实例: {{ .Labels.instance }}
      - 恢复时间: {{ .EndsAt.Format "2006-01-02 15:04:05" }}
    {{ end }}
    {{ end }}
    {{ end }}

---
# 更新AlertManager配置以使用模板
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config-with-templates
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.example.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'your-password'
      
    # 模板配置
    templates:
      - '/etc/alertmanager/templates/*.tmpl'
      
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default-receiver'
      routes:
        - match:
            severity: critical
          receiver: 'level1-alerts'
          group_wait: 5s
          repeat_interval: 30m
        - match:
            severity: warning
          receiver: 'level2-alerts'
          group_wait: 10s
          repeat_interval: 1h
        - match:
            severity: info
          receiver: 'level3-alerts'
          group_wait: 30s
          repeat_interval: 4h

    receivers:
      - name: 'default-receiver'
        webhook_configs:
          - url: 'http://webhook-service:8080/alerts'
            
      - name: 'level1-alerts'
        # 使用自定义模板的Slack配置
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
            channel: '@oncall-engineer'
            title: '{{ template "slack.title" . }}'
            text: '{{ template "slack.text" . }}'
            send_resolved: true
        # 邮件通知
        email_configs:
          - to: '<EMAIL>'
            subject: '{{ template "email.subject" . }}'
            html: '{{ template "email.html" . }}'
            
      - name: 'level2-alerts'
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
            channel: '@oncall-engineer'
            title: '{{ template "slack.title" . }}'
            text: '{{ template "slack.text" . }}'
            send_resolved: true
            
      - name: 'level3-alerts'
        slack_configs:
          - api_url: 'https://hooks.slack.com/services/YOUR/TEAM/WEBHOOK'
            channel: '#ops-alerts'
            title: '{{ template "slack.title" . }}'
            text: '{{ template "slack.text" . }}'
            send_resolved: true

    inhibit_rules:
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'cluster', 'service']
